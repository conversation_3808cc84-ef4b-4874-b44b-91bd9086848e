# 👨‍💻 Guide Développeur - Système de Messagerie LinCom

## 🏗️ Architecture du Système

### Couches de l'Application
```
┌─────────────────────────────────────┐
│           Interface Web             │
│        (messagerie.aspx)            │
├─────────────────────────────────────┤
│         Code-Behind                 │
│      (messagerie.aspx.cs)           │
├─────────────────────────────────────┤
│      Couche <PERSON>tier                  │
│   (MessageImp, ConversationImp)     │
├─────────────────────────────────────┤
│       Modèles de Données            │
│  (Message, Conversation, etc.)      │
├─────────────────────────────────────┤
│      Base de Données                │
│      (SQL Server + EF)              │
└─────────────────────────────────────┘
```

## 🔧 APIs Principales

### MessageImp.cs
```csharp
// Envoi de message avec gestion automatique des statuts
int AjouterMessageEtStatusPourTous(long conversationId, long senderId, 
    string contenu, string attachmentUrl = null)

// Marquage des messages comme lus
int MarquerTousMessagesCommeLus(long conversationId, long userId)

// Chargement des messages d'une conversation
void ChargerMessages(Repeater rpt, long conversationId, int nombreMessages)
```

### ConversationImp.cs
```csharp
// Vérification/Création automatique de conversation
long VerifierConversationId(long membre1Id, long membre2Id)

// Gestion des participants
int AjouterParticipant(long conversationId, long membreId)
bool ParticipantExiste(long conversationId, long membreId)
```

## 📁 Structure des Fichiers

### Dossiers Importants
- `/file/messages/` - Pièces jointes des messages
- `/file/membr/` - Photos de profil des membres
- `/images/` - Images par défaut (avatars, etc.)

### Conventions de Nommage
- Fichiers uploadés : `yyyyMMddHHmmss_nomoriginal.ext`
- Photos profil : `membre_{id}.{ext}`
- Images par défaut : `default-{type}.png`

## 🎨 Personnalisation CSS

### Classes CSS Principales
```css
.chat-wrapper          /* Container principal */
.contacts-panel        /* Sidebar des contacts */
.chat-panel           /* Zone de conversation */
.message-container    /* Container d'un message */
.sent / .received     /* Messages envoyés/reçus */
.attachment-link      /* Liens vers pièces jointes */
.typing-indicator     /* Indicateur de frappe */
```

### Variables CSS Personnalisables
```css
:root {
  --primary-color: #008374;
  --secondary-color: #6c757d;
  --success-color: #28a745;
  --danger-color: #dc3545;
  --border-radius: 10px;
}
```

## 🔒 Sécurité

### Validations Implémentées
1. **Types de fichiers** - Liste blanche d'extensions
2. **Taille des fichiers** - Maximum 5MB
3. **Permissions utilisateur** - Vérification des droits
4. **Échappement XSS** - Sanitisation des entrées

### Bonnes Pratiques
```csharp
// Toujours valider côté serveur
if (!ValidateFileUpload(file)) return;

// Utiliser des transactions pour la cohérence
using (var transaction = con.Database.BeginTransaction())
{
    // Opérations multiples
    transaction.Commit();
}

// Échapper les caractères spéciaux
string safeMessage = HttpUtility.HtmlEncode(userInput);
```

## 🧪 Tests et Débogage

### Points de Test Critiques
1. **Upload de fichiers** - Tous types et tailles
2. **Gestion d'erreurs** - Scénarios d'échec
3. **Performance** - Conversations avec beaucoup de messages
4. **Sécurité** - Tentatives d'injection

### Logs et Débogage
```csharp
// Ajouter des logs pour le débogage
System.Diagnostics.Debug.WriteLine($"Conversation ID: {conversationId}");

// Utiliser try-catch avec logs détaillés
catch (Exception ex)
{
    System.Diagnostics.Debug.WriteLine($"Erreur: {ex.Message}");
    // Log vers fichier ou base de données
}
```

## 🚀 Déploiement

### Prérequis
- .NET Framework 4.8
- SQL Server (LocalDB ou complet)
- IIS ou IIS Express
- Dossiers de fichiers avec permissions d'écriture

### Configuration
1. Vérifier les chaînes de connexion dans `Web.config`
2. Créer les dossiers de fichiers avec bonnes permissions
3. Configurer les paramètres SMTP si nécessaire
4. Tester les uploads de fichiers

## 📈 Optimisations Futures

### Performance
- Pagination des messages anciens
- Cache des conversations récentes
- Compression des images uploadées
- Index de base de données optimisés

### Fonctionnalités
- SignalR pour temps réel
- Notifications push
- Recherche full-text
- Messages vocaux
- Émojis et formatage riche

## 🐛 Résolution de Problèmes

### Problèmes Courants
1. **Fichiers non uploadés** - Vérifier permissions dossier
2. **Messages non affichés** - Vérifier ID utilisateur connecté
3. **Erreurs JavaScript** - Vérifier IDs des contrôles
4. **Performance lente** - Optimiser requêtes LINQ

### Outils de Diagnostic
- Profiler SQL Server
- Outils développeur navigateur
- Logs IIS
- Event Viewer Windows
