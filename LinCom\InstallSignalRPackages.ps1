# Script PowerShell pour installer les packages SignalR nécessaires
# Exécuter ce script dans la Console du Gestionnaire de Package de Visual Studio

Write-Host "Installation des packages SignalR pour LinCom..." -ForegroundColor Green

# Installer les packages SignalR
Install-Package Microsoft.AspNet.SignalR -Version 2.4.3
Install-Package Microsoft.AspNet.SignalR.Core -Version 2.4.3
Install-Package Microsoft.AspNet.SignalR.SystemWeb -Version 2.4.3
Install-Package Microsoft.Owin -Version 4.2.2
Install-Package Microsoft.Owin.Host.SystemWeb -Version 4.2.2
Install-Package Microsoft.Owin.Security -Version 4.2.2
Install-Package Owin -Version 1.0

Write-Host "Installation terminée!" -ForegroundColor Green
Write-Host "Les packages SignalR ont été installés avec succès." -ForegroundColor Yellow
Write-Host "Vous pouvez maintenant compiler et exécuter le projet." -ForegroundColor Yellow
