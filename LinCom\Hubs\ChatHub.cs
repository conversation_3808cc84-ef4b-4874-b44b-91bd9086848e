using Microsoft.AspNet.SignalR;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Web;

namespace LinCom.Hubs
{
    public class ChatHub : Hub
    {
        // Dictionnaire pour stocker les connexions des utilisateurs
        private static Dictionary<string, string> ConnectedUsers = new Dictionary<string, string>();

        public override Task OnConnected()
        {
            string userId = Context.QueryString["userId"];
            if (!string.IsNullOrEmpty(userId))
            {
                ConnectedUsers[userId] = Context.ConnectionId;
                
                // Notifier les autres utilisateurs que cet utilisateur est en ligne
                Clients.Others.UserConnected(userId);
            }
            return base.OnConnected();
        }

        public override Task OnDisconnected(bool stopCalled)
        {
            string userId = ConnectedUsers.FirstOrDefault(x => x.Value == Context.ConnectionId).Key;
            if (!string.IsNullOrEmpty(userId))
            {
                ConnectedUsers.Remove(userId);
                
                // Notifier les autres utilisateurs que cet utilisateur est hors ligne
                Clients.Others.UserDisconnected(userId);
            }
            return base.OnDisconnected(stopCalled);
        }

        // Méthode pour envoyer un message
        public void SendMessage(string toUserId, string message, string senderName, string senderPhoto)
        {
            if (ConnectedUsers.ContainsKey(toUserId))
            {
                string connectionId = ConnectedUsers[toUserId];
                Clients.Client(connectionId).ReceiveMessage(Context.QueryString["userId"], message, senderName, senderPhoto, DateTime.Now.ToString("HH:mm"));
            }
        }

        // Méthode pour notifier qu'un utilisateur est en train d'écrire
        public void StartTyping(string toUserId, string senderName)
        {
            if (ConnectedUsers.ContainsKey(toUserId))
            {
                string connectionId = ConnectedUsers[toUserId];
                Clients.Client(connectionId).UserStartedTyping(Context.QueryString["userId"], senderName);
            }
        }

        // Méthode pour notifier qu'un utilisateur a arrêté d'écrire
        public void StopTyping(string toUserId)
        {
            if (ConnectedUsers.ContainsKey(toUserId))
            {
                string connectionId = ConnectedUsers[toUserId];
                Clients.Client(connectionId).UserStoppedTyping(Context.QueryString["userId"]);
            }
        }

        // Méthode pour envoyer un message à un groupe
        public void SendGroupMessage(string groupId, string message, string senderName, string senderPhoto)
        {
            Clients.Group(groupId).ReceiveGroupMessage(Context.QueryString["userId"], message, senderName, senderPhoto, DateTime.Now.ToString("HH:mm"));
        }

        // Méthode pour rejoindre un groupe
        public void JoinGroup(string groupId)
        {
            Groups.Add(Context.ConnectionId, groupId);
        }

        // Méthode pour quitter un groupe
        public void LeaveGroup(string groupId)
        {
            Groups.Remove(Context.ConnectionId, groupId);
        }

        // Méthode pour obtenir les utilisateurs en ligne
        public void GetOnlineUsers()
        {
            Clients.Caller.OnlineUsers(ConnectedUsers.Keys.ToList());
        }

        // Méthode pour marquer un message comme lu
        public void MarkAsRead(string fromUserId, string messageId)
        {
            if (ConnectedUsers.ContainsKey(fromUserId))
            {
                string connectionId = ConnectedUsers[fromUserId];
                Clients.Client(connectionId).MessageMarkedAsRead(messageId, Context.QueryString["userId"]);
            }
        }
    }
}
