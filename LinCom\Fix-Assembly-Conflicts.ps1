# Script PowerShell pour résoudre les conflits d'assemblies SignalR/OWIN
# Exécuter dans la Console du Gestionnaire de Package de Visual Studio

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   Résolution Conflits d'Assemblies" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "[1/5] Nettoyage des packages existants..." -ForegroundColor Yellow

# Désinstaller les packages dans l'ordre inverse des dépendances
try {
    Uninstall-Package Microsoft.AspNet.SignalR -Force -RemoveDependencies
    Write-Host "✅ SignalR désinstallé" -ForegroundColor Green
} catch {
    Write-Host "⚠️  SignalR déjà désinstallé ou non trouvé" -ForegroundColor Yellow
}

try {
    Uninstall-Package Microsoft.Owin.Host.SystemWeb -Force
    Uninstall-Package Microsoft.Owin.Security -Force
    Uninstall-Package Microsoft.Owin -Force
    Write-Host "✅ Packages OWIN désinstallés" -ForegroundColor Green
} catch {
    Write-Host "⚠️  Packages OWIN déjà désinstallés" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "[2/5] Installation des packages OWIN avec versions compatibles..." -ForegroundColor Yellow

# Installer OWIN en premier avec versions spécifiques
Install-Package Owin -Version 1.0
Install-Package Microsoft.Owin -Version 4.2.2
Install-Package Microsoft.Owin.Security -Version 4.2.2
Install-Package Microsoft.Owin.Host.SystemWeb -Version 4.2.2

Write-Host "✅ Packages OWIN installés" -ForegroundColor Green

Write-Host ""
Write-Host "[3/5] Installation de SignalR avec versions compatibles..." -ForegroundColor Yellow

# Installer SignalR avec versions spécifiques
Install-Package Microsoft.AspNet.SignalR.Core -Version 2.4.3
Install-Package Microsoft.AspNet.SignalR.SystemWeb -Version 2.4.3
Install-Package Microsoft.AspNet.SignalR -Version 2.4.3

Write-Host "✅ SignalR installé" -ForegroundColor Green

Write-Host ""
Write-Host "[4/5] Vérification des redirections d'assemblies..." -ForegroundColor Yellow

$webConfigPath = "Web.config"
if (Test-Path $webConfigPath) {
    $webConfig = Get-Content $webConfigPath -Raw
    
    $hasOwinRedirect = $webConfig -match "Microsoft\.Owin.*4\.2\.2\.0"
    $hasSignalRRedirect = $webConfig -match "Microsoft\.AspNet\.SignalR\.Core.*2\.4\.3\.0"
    
    if ($hasOwinRedirect -and $hasSignalRRedirect) {
        Write-Host "✅ Redirections d'assemblies présentes" -ForegroundColor Green
    } else {
        Write-Host "⚠️  Vérifiez les redirections dans Web.config" -ForegroundColor Yellow
    }
} else {
    Write-Host "⚠️  Web.config non trouvé" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "[5/5] Nettoyage et reconstruction..." -ForegroundColor Yellow

# Instructions pour Visual Studio
Write-Host "Maintenant dans Visual Studio :" -ForegroundColor Cyan
Write-Host "1. Build → Clean Solution" -ForegroundColor White
Write-Host "2. Build → Rebuild Solution" -ForegroundColor White
Write-Host "3. Vérifier qu'il n'y a aucune erreur" -ForegroundColor White
Write-Host "4. F5 pour tester l'application" -ForegroundColor White

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "           RÉSOLUTION TERMINÉE" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

Write-Host ""
Write-Host "📋 Packages installés :" -ForegroundColor Green
Write-Host "- Owin 1.0" -ForegroundColor White
Write-Host "- Microsoft.Owin 4.2.2" -ForegroundColor White
Write-Host "- Microsoft.Owin.Security 4.2.2" -ForegroundColor White
Write-Host "- Microsoft.Owin.Host.SystemWeb 4.2.2" -ForegroundColor White
Write-Host "- Microsoft.AspNet.SignalR.Core 2.4.3" -ForegroundColor White
Write-Host "- Microsoft.AspNet.SignalR.SystemWeb 2.4.3" -ForegroundColor White
Write-Host "- Microsoft.AspNet.SignalR 2.4.3" -ForegroundColor White

Write-Host ""
Write-Host "🔧 Redirections d'assemblies configurées dans Web.config" -ForegroundColor Green
Write-Host ""
Write-Host "🎯 L'erreur de conflit d'assemblies devrait être résolue !" -ForegroundColor Green
