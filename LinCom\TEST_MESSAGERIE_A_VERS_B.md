# 🧪 Test Complet Messagerie A → B - LinCom

## 📋 Objectif du Test

Vérifier que l'utilisateur A peut envoyer des messages (texte et pièces jointes) à l'utilisateur B, et que B les reçoit instantanément sans problème.

## 🚀 Préparation du Test

### **1. Compilation et Démarrage**
```
1. Ouvrir Visual Studio 2022
2. Ouvrir LinCom.sln
3. Build → Rebuild Solution (vérifier 0 erreurs)
4. F5 pour démarrer l'application
5. Noter l'URL (ex: https://localhost:44319/)
```

### **2. Préparation des Navigateurs**
```
Navigateur 1 (Utilisateur A):
- Chrome/Edge normal
- Aller sur https://localhost:44319/
- Se connecter avec le premier utilisateur

Navigateur 2 (Utilisateur B):
- Chrome/Edge en mode incognito OU Firefox
- Aller sur https://localhost:44319/
- Se connecter avec le deuxième utilisateur
```

### **3. Vérification des Connexions SignalR**
Dans chaque navigateur :
```
1. Aller sur /messagerie.aspx
2. Ouvrir Console Développeur (F12)
3. Vérifier le message "SignalR connecté"
4. Vérifier qu'il n'y a pas d'erreurs rouges
```

## 🧪 **Test 1 : Messages Texte Simple**

### **Étape 1 : Utilisateur A envoie un message**
```
Dans le Navigateur 1 (Utilisateur A):
1. Aller sur /messagerie.aspx
2. Dans l'onglet "Contacts", sélectionner l'Utilisateur B
3. Taper le message : "Bonjour B, ceci est un test de A"
4. Cliquer sur "Envoyer" ou appuyer Entrée
5. Vérifier que le message apparaît avec 2 coches vertes
```

### **Étape 2 : Utilisateur B reçoit le message**
```
Dans le Navigateur 2 (Utilisateur B):
1. Rester sur /messagerie.aspx
2. Vérifier que le message de A apparaît INSTANTANÉMENT
3. Vérifier le contenu : "Bonjour B, ceci est un test de A"
4. Vérifier l'heure d'envoi
5. Vérifier qu'une notification sonore a été jouée
```

### **✅ Résultat Attendu**
- Message visible chez A avec statut "envoyé"
- Message reçu instantanément chez B
- Notification sonore chez B
- Aucune erreur dans les consoles

## 🧪 **Test 2 : Messages avec Pièces Jointes**

### **Étape 1 : Préparation des fichiers de test**
Créer ces fichiers sur le bureau :
```
- test_document.pdf (ou .txt)
- test_image.jpg (ou .png)
- test_archive.zip
```

### **Étape 2 : A envoie un message avec pièce jointe**
```
Dans le Navigateur 1 (Utilisateur A):
1. Sélectionner l'Utilisateur B
2. Cliquer sur l'icône 📎 (Joindre un fichier)
3. Sélectionner "test_document.pdf"
4. Taper le message : "Voici un document pour toi"
5. Cliquer "Envoyer"
6. Vérifier que le message apparaît avec :
   - Le texte du message
   - L'icône de pièce jointe
   - Le nom du fichier
   - La taille du fichier
   - 2 coches vertes
```

### **Étape 3 : B reçoit le message avec pièce jointe**
```
Dans le Navigateur 2 (Utilisateur B):
1. Vérifier que le message apparaît INSTANTANÉMENT
2. Vérifier la présence de :
   - Texte : "Voici un document pour toi"
   - Icône 📎 de pièce jointe
   - Nom du fichier : "test_document.pdf"
   - Taille du fichier (ex: "1.2 KB")
   - Lien de téléchargement cliquable
3. Cliquer sur le lien de la pièce jointe
4. Vérifier que le fichier se télécharge/s'ouvre SANS erreur 404
```

### **✅ Résultat Attendu**
- Message avec pièce jointe visible chez A
- Message reçu instantanément chez B avec pièce jointe
- Lien de téléchargement fonctionnel
- Aucune erreur 404 lors du clic

## 🧪 **Test 3 : Messages Bidirectionnels**

### **Étape 1 : B répond à A**
```
Dans le Navigateur 2 (Utilisateur B):
1. Taper la réponse : "Merci A, j'ai bien reçu ton message et le fichier !"
2. Cliquer "Envoyer"
3. Vérifier l'affichage avec 2 coches vertes
```

### **Étape 2 : A reçoit la réponse**
```
Dans le Navigateur 1 (Utilisateur A):
1. Vérifier que la réponse de B apparaît INSTANTANÉMENT
2. Vérifier le contenu du message
3. Vérifier la notification sonore
```

### **✅ Résultat Attendu**
- Communication bidirectionnelle fluide
- Messages instantanés dans les deux sens
- Notifications appropriées

## 🧪 **Test 4 : Différents Types de Fichiers**

### **Test avec Image**
```
Utilisateur A envoie une image (.jpg/.png):
1. Joindre "test_image.jpg"
2. Message : "Voici une image"
3. Envoyer
4. B vérifie la réception et le téléchargement
```

### **Test avec Archive**
```
Utilisateur A envoie une archive (.zip):
1. Joindre "test_archive.zip"
2. Message : "Archive de documents"
3. Envoyer
4. B vérifie la réception et le téléchargement
```

### **✅ Résultat Attendu**
- Tous les types de fichiers autorisés fonctionnent
- Validation des types côté client et serveur
- Téléchargements sans erreur

## 🧪 **Test 5 : Gestion des Erreurs**

### **Test Fichier Trop Volumineux**
```
1. Essayer d'envoyer un fichier > 5MB
2. Vérifier le message d'erreur approprié
3. Vérifier que l'envoi est bloqué
```

### **Test Type de Fichier Non Autorisé**
```
1. Essayer d'envoyer un fichier .exe ou .bat
2. Vérifier le message d'erreur
3. Vérifier que l'envoi est bloqué
```

### **✅ Résultat Attendu**
- Validations fonctionnelles
- Messages d'erreur clairs
- Sécurité maintenue

## 🧪 **Test 6 : Performance et Stabilité**

### **Test Messages Multiples**
```
1. A envoie 5 messages consécutifs rapidement
2. B vérifie qu'il reçoit tous les messages dans l'ordre
3. Vérifier qu'il n'y a pas de perte de messages
```

### **Test Reconnexion**
```
1. Fermer/rouvrir un navigateur
2. Vérifier que SignalR se reconnecte automatiquement
3. Tester l'envoi de messages après reconnexion
```

### **✅ Résultat Attendu**
- Aucune perte de messages
- Reconnexion automatique
- Performance stable

## 📊 **Checklist de Validation Complète**

### **✅ Messages Texte**
- [ ] A → B : Message simple reçu instantanément
- [ ] B → A : Réponse reçue instantanément
- [ ] Notifications sonores fonctionnelles
- [ ] Statuts de lecture corrects (2 coches vertes)

### **✅ Pièces Jointes**
- [ ] PDF : Envoi et réception OK
- [ ] Image : Envoi et réception OK
- [ ] Archive : Envoi et réception OK
- [ ] Liens de téléchargement fonctionnels
- [ ] Aucune erreur 404

### **✅ Interface Utilisateur**
- [ ] Messages s'affichent correctement
- [ ] Informations de fichiers visibles (nom, taille)
- [ ] Icônes appropriées
- [ ] Scroll automatique vers le bas

### **✅ SignalR et Temps Réel**
- [ ] Connexion SignalR établie
- [ ] Messages instantanés
- [ ] Notifications push
- [ ] Reconnexion automatique

### **✅ Sécurité et Validation**
- [ ] Types de fichiers validés
- [ ] Taille maximale respectée
- [ ] Messages d'erreur appropriés
- [ ] Upload sécurisé

## 🚨 **Résolution de Problèmes**

### **Si les messages ne sont pas reçus :**
```
1. Vérifier la console développeur pour erreurs SignalR
2. Vérifier que les deux utilisateurs sont connectés
3. Recharger les pages et retester
4. Vérifier les logs serveur dans Visual Studio
```

### **Si les pièces jointes ne fonctionnent pas :**
```
1. Vérifier que le dossier file/messages/ existe
2. Vérifier les permissions d'écriture
3. Vérifier la taille du fichier (< 5MB)
4. Vérifier le type de fichier autorisé
```

### **Si SignalR ne se connecte pas :**
```
1. Vérifier que les packages SignalR sont installés
2. Vérifier Web.config pour les redirections d'assemblies
3. Rebuild la solution
4. Redémarrer IIS Express
```

## 🎯 **Critères de Succès**

### **✅ Test RÉUSSI si :**
- Tous les messages texte sont reçus instantanément
- Toutes les pièces jointes sont transmises et téléchargeables
- Aucune erreur 404 sur les fichiers
- Notifications sonores fonctionnelles
- Interface utilisateur fluide et responsive

### **❌ Test ÉCHOUÉ si :**
- Messages non reçus ou avec délai
- Erreurs 404 sur les pièces jointes
- Erreurs SignalR dans la console
- Interface qui ne répond pas

## 🏆 **Résultat Attendu Final**

**LinCom doit démontrer une messagerie temps réel parfaitement fonctionnelle avec :**
- ✅ **Transmission instantanée** des messages texte
- ✅ **Upload et partage** de fichiers sans erreur
- ✅ **Notifications temps réel** complètes
- ✅ **Interface utilisateur** moderne et fluide
- ✅ **Sécurité** et validation appropriées

**Si tous les tests passent, LinCom est prêt pour la production ! 🚀**
