﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Antlr" version="*******" targetFramework="net48" />
  <package id="AspNet.ScriptManager.bootstrap" version="5.2.3" targetFramework="net48" />
  <package id="AspNet.ScriptManager.jQuery" version="3.7.1" targetFramework="net48" />
  <package id="BCrypt.Net-Next" version="4.0.3" targetFramework="net48" />
  <package id="bootstrap" version="5.2.3" targetFramework="net48" />
  <package id="EntityFramework" version="6.5.1" targetFramework="net48" />
  <package id="jQuery" version="3.7.1" targetFramework="net48" />
  <package id="Microsoft.AspNet.FriendlyUrls" version="1.0.2" targetFramework="net48" />
  <package id="Microsoft.AspNet.FriendlyUrls.Core" version="1.0.2" targetFramework="net48" />
  <package id="Microsoft.AspNet.Mvc" version="5.2.9" targetFramework="net48" />
  <package id="Microsoft.AspNet.Razor" version="3.2.9" targetFramework="net48" />
  <package id="Microsoft.AspNet.ScriptManager.MSAjax" version="5.0.0" targetFramework="net48" />
  <package id="Microsoft.AspNet.ScriptManager.WebForms" version="5.0.0" targetFramework="net48" />
  <package id="Microsoft.AspNet.Web.Optimization" version="1.1.3" targetFramework="net48" />
  <package id="Microsoft.AspNet.Web.Optimization.WebForms" version="1.1.3" targetFramework="net48" />
  <package id="Microsoft.AspNet.WebApi" version="5.2.9" targetFramework="net48" />
  <package id="Microsoft.AspNet.WebApi.Client" version="5.2.9" targetFramework="net48" />
  <package id="Microsoft.AspNet.WebApi.Core" version="5.2.9" targetFramework="net48" />
  <package id="Microsoft.AspNet.WebApi.WebHost" version="5.2.9" targetFramework="net48" />
  <package id="Microsoft.AspNet.WebPages" version="3.2.9" targetFramework="net48" />
  <package id="Microsoft.CodeDom.Providers.DotNetCompilerPlatform" version="4.1.0" targetFramework="net48" />
  <package id="Microsoft.Web.Infrastructure" version="2.0.1" targetFramework="net48" />
  <package id="Modernizr" version="2.8.3" targetFramework="net48" />
  <package id="Newtonsoft.Json" version="13.0.3" targetFramework="net48" />
  <package id="System.Buffers" version="4.5.1" targetFramework="net48" />
  <package id="System.Memory" version="4.5.4" targetFramework="net48" />
  <package id="System.Numerics.Vectors" version="4.5.0" targetFramework="net48" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="4.5.3" targetFramework="net48" />
  <package id="WebGrease" version="1.6.0" targetFramework="net48" />
</packages>