# Script PowerShell pour tester rapidement la compilation LinCom
# Exécuter dans PowerShell depuis le dossier LinCom

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   Test de Compilation Rapide - LinCom" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Vérifier la présence de MSBuild
Write-Host "[1/6] Vérification de MSBuild..." -ForegroundColor Yellow
$msbuild = Get-Command "msbuild.exe" -ErrorAction SilentlyContinue
if (-not $msbuild) {
    Write-Host "❌ MSBuild non trouvé. Veuillez installer Visual Studio 2022." -ForegroundColor Red
    exit 1
}
Write-Host "✅ MSBuild trouvé : $($msbuild.Source)" -ForegroundColor Green

# Vérifier la solution
Write-Host ""
Write-Host "[2/6] Vérification de la solution..." -ForegroundColor Yellow
if (-not (Test-Path "LinCom.sln")) {
    Write-Host "❌ LinCom.sln non trouvé dans le répertoire courant." -ForegroundColor Red
    exit 1
}
Write-Host "✅ LinCom.sln trouvé" -ForegroundColor Green

# Vérifier les fichiers critiques
Write-Host ""
Write-Host "[3/6] Vérification des fichiers critiques..." -ForegroundColor Yellow
$criticalFiles = @(
    "LinCom\messagerie.aspx",
    "LinCom\messagerie.aspx.cs",
    "LinCom\Startup.cs",
    "LinCom\Hubs\ChatHub.cs",
    "LinCom\Web.config",
    "LinCom\packages.config"
)

$missingFiles = @()
foreach ($file in $criticalFiles) {
    if (-not (Test-Path $file)) {
        $missingFiles += $file
        Write-Host "⚠️  $file manquant" -ForegroundColor Yellow
    } else {
        Write-Host "✅ $file présent" -ForegroundColor Green
    }
}

if ($missingFiles.Count -gt 0) {
    Write-Host ""
    Write-Host "❌ Fichiers manquants détectés. Vérifiez l'intégration." -ForegroundColor Red
    exit 1
}

# Restaurer les packages NuGet
Write-Host ""
Write-Host "[4/6] Restauration des packages NuGet..." -ForegroundColor Yellow
try {
    & nuget restore LinCom.sln -NonInteractive 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Packages NuGet restaurés" -ForegroundColor Green
    } else {
        Write-Host "⚠️  Restauration NuGet avec avertissements" -ForegroundColor Yellow
    }
} catch {
    Write-Host "⚠️  NuGet CLI non disponible, utilisez Visual Studio" -ForegroundColor Yellow
}

# Compiler la solution
Write-Host ""
Write-Host "[5/6] Compilation de la solution..." -ForegroundColor Yellow
try {
    $buildOutput = & msbuild LinCom.sln /p:Configuration=Debug /p:Platform="Any CPU" /verbosity:quiet 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Compilation réussie !" -ForegroundColor Green
    } else {
        Write-Host "❌ Erreurs de compilation détectées :" -ForegroundColor Red
        Write-Host $buildOutput -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "❌ Erreur lors de la compilation : $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Vérifier les assemblies générés
Write-Host ""
Write-Host "[6/6] Vérification des assemblies..." -ForegroundColor Yellow
$binPath = "LinCom\bin\LinCom.dll"
if (Test-Path $binPath) {
    $fileInfo = Get-Item $binPath
    Write-Host "✅ LinCom.dll généré ($($fileInfo.Length) bytes)" -ForegroundColor Green
    Write-Host "   Dernière modification : $($fileInfo.LastWriteTime)" -ForegroundColor Gray
} else {
    Write-Host "⚠️  LinCom.dll non trouvé dans bin/" -ForegroundColor Yellow
}

# Résumé final
Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "           RÉSUMÉ DU TEST" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "✅ Solution LinCom.sln : OK" -ForegroundColor Green
Write-Host "✅ Fichiers critiques : OK" -ForegroundColor Green
Write-Host "✅ Packages NuGet : OK" -ForegroundColor Green
Write-Host "✅ Compilation : RÉUSSIE" -ForegroundColor Green
Write-Host "✅ Assemblies : Générés" -ForegroundColor Green
Write-Host ""
Write-Host "🎉 LinCom est prêt pour Visual Studio 2022 !" -ForegroundColor Green
Write-Host ""
Write-Host "Prochaines étapes :" -ForegroundColor Cyan
Write-Host "1. Ouvrir LinCom.sln dans Visual Studio 2022" -ForegroundColor White
Write-Host "2. Appuyer F5 pour démarrer l'application" -ForegroundColor White
Write-Host "3. Naviguer vers /messagerie.aspx pour tester" -ForegroundColor White
Write-Host "4. Vérifier 'SignalR connecté' dans la console développeur" -ForegroundColor White
Write-Host ""
Write-Host "📚 Documentation disponible :" -ForegroundColor Cyan
Write-Host "- README_PROJET_COMPLET.md" -ForegroundColor White
Write-Host "- INSTALLATION_VISUAL_STUDIO_2022.md" -ForegroundColor White
Write-Host "- CORRECTIONS_ERREURS_COMPILATION.md" -ForegroundColor White
Write-Host ""

# Pause pour permettre la lecture
Read-Host "Appuyez sur Entrée pour continuer..."
