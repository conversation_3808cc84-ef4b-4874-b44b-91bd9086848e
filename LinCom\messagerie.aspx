﻿<%@ Page Title="" Language="C#" MasterPageFile="~/PageMaster.Master" AutoEventWireup="true" CodeBehind="messagerie.aspx.cs" Inherits="LinCom.messagerie" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <!-- Page Title -->
    <div class="page-title">
        <div class="heading">
            <div class="container">
                <div class="row d-flex justify-content-center text-center">
                    <div class="col-lg-8">
                    </div>
                </div>
            </div>
        </div>
        <nav class="breadcrumbs">
            <div class="container">
                <ol>
                    <li><a href="home.aspx">Home</a></li>
                    <li><a href="#">Espace FNUAP</a></li>
                    <li><a href="#">Bibliothèque Digitale de FNUAP</a></li>
                    <li class="current"><a href="ressources-document.aspx">Documents officiels</a></li>
                </ol>
            </div>
        </nav>
    </div>
    <!-- End Page Title -->
    <main class="main">
        <asp:HiddenField ID="hdnConversationId" runat="server" />
<asp:HiddenField ID="hdnIsGroup" runat="server" />
<asp:HiddenField ID="hdnCurrentUserId" runat="server" />

        <div class="container py-4">
            <h2 class="mb-4">💬 Espace Messagerie</h2>

            <div class="chat-wrapper">

                <!-- Sidebar -->
                <div class="contacts-panel">
                    <div class="contacts-header">
                        <div class="header-tabs">
                            <button class="tab-btn active" onclick="showTab('contacts')">👥 Contacts</button>
                            <button class="tab-btn" onclick="showTab('groups')">👥 Groupes</button>
                        </div>
                        <button type="button" class="btn-create-group" onclick="showCreateGroupModal(); return false;" title="Créer un groupe">
                            <i class="bi bi-plus-circle"></i>
                        </button>
                    </div>
                    <div class="contacts-search">
                        <input type="text" id="searchInput" placeholder="Rechercher..." onkeyup="filterContacts()">
                    </div>
                    <asp:ListView ID="listmembre" runat="server" OnItemCommand="listmembre_ItemCommand">
                        <EmptyDataTemplate>Aucune Donnée</EmptyDataTemplate>
                        <ItemTemplate>
                            <asp:LinkButton ID="btnSelectMembre" runat="server"
                                CommandName="viewmem"
                                CommandArgument='<%# Eval("id") %>'
                                CssClass="contact-item">
        <img src='<%# HttpUtility.HtmlEncode(string.Concat("../file/membr/", Eval("PhotoProfil"))) %>' alt="Nom du Membre" />
        <div>
            <div class="contact-name"><%# HttpUtility.HtmlEncode(Eval("Membre")) %></div>
        </div>
                            </asp:LinkButton>
                                <!-- 🔽 Champ caché pour dire si c’est un groupe -->
 
                        </ItemTemplate>
                    </asp:ListView>

                    <!-- Section Groupes -->
                    <div id="groupsSection" class="contact-section" style="display: none;">
                        <asp:ListView ID="listGroupes" runat="server" OnItemCommand="listGroupes_ItemCommand">
                            <EmptyDataTemplate>
                                <div class="empty-state">
                                    <i class="bi bi-people-fill"></i>
                                    <p>Aucun groupe créé</p>
                                    <button type="button" class="btn-create-first-group" onclick="showCreateGroupModal(); return false;">
                                        Créer votre premier groupe
                                    </button>
                                </div>
                            </EmptyDataTemplate>
                            <ItemTemplate>
                                <asp:LinkButton ID="btnSelectGroupe" runat="server"
                                    CommandName="viewgroup"
                                    CommandArgument='<%# Eval("ConversationId") %>'
                                    CssClass="contact-item group-item" data-type="group">
                                    <div class="group-avatar">
                                        <i class="bi bi-people-fill"></i>
                                    </div>
                                    <div class="contact-info">
                                        <div class="contact-name"><%# HttpUtility.HtmlEncode(Eval("Sujet")) %></div>
                                        <div class="contact-status"><%# Eval("ParticipantCount") %> membres</div>
                                    </div>
                                    <div class="unread-count" style="display:none;">0</div>
                                </asp:LinkButton>
                            </ItemTemplate>
                        </asp:ListView>
                    </div>

                </div>


                <!-- Main Chat Area -->
                <div class="chat-panel">

                    <div class="chat-header">
                        <div class="header-left">
                            <asp:Label ID="lblHeader" runat="server" Text="Sélectionnez un contact pour discuter"></asp:Label>
                            <asp:Label ID="lblId" Visible="false" runat="server" Text="0"></asp:Label>
                        </div>
                        <div class="header-actions">
                            <button type="button" class="btn-header-action" onclick="showSearchModal()" title="Rechercher dans les messages">
                                <i class="bi bi-search"></i>
                            </button>
                            <button type="button" class="btn-header-action" onclick="toggleEmojiPicker()" title="Émojis">
                                <i class="bi bi-emoji-smile"></i>
                            </button>
                            <button type="button" class="btn-header-action" onclick="showGroupInfo()" title="Informations du groupe" id="btnGroupInfo" style="display: none;">
                                <i class="bi bi-info-circle"></i>
                            </button>
                        </div>
                    </div>

                    <div class="chat-body">
                      <asp:Repeater ID="rptMessages" runat="server">
    <ItemTemplate>
        <div class='message-container <%# Convert.ToInt64(Eval("SenderId")) == GetCurrentUserId() ? "sent" : "received" %> new-message'>
            <div class="message-header">
                <img class="avatar" src='<%# ResolveUrl("~/file/membr/") + Eval("Photomembre") %>' alt="Photo"
                     onerror="this.src='<%# ResolveUrl("~/images/default-avatar.png") %>'" />
                <strong><%# Eval("Expediteur") %></strong>
                <span class="date"><%# Eval("DateEnvoi", "{0:dd MMM yyyy HH:mm}") %></span>
            </div>

            <div class="message-body">
                <%-- Contenu du message --%>
                <asp:Panel runat="server" Visible='<%# !string.IsNullOrEmpty(Eval("Contenu").ToString()) %>'>
                    <p><%# Eval("Contenu") %></p>
                </asp:Panel>

                <%-- Pièce jointe si présente --%>
                <asp:Panel runat="server" Visible='<%# !string.IsNullOrEmpty(Eval("AttachmentUrl").ToString()) %>'>
                    <div class="attachment-container">
                        <a href='<%# ResolveAttachmentUrl(Eval("AttachmentUrl").ToString()) %>' target="_blank" class="attachment-link">
                            <i class="bi bi-paperclip"></i>
                            <%# GetFileName(Eval("AttachmentUrl").ToString()) %>
                        </a>
                        <span class="attachment-size"><%# GetFileSize(Eval("AttachmentUrl").ToString()) %></span>
                    </div>
                </asp:Panel>

                <%-- Indicateur de statut pour les messages envoyés --%>
                <asp:Panel runat="server" Visible='<%# Convert.ToInt64(Eval("SenderId")) == GetCurrentUserId() %>' CssClass="message-status">
                    <span class="status-indicator">
                        <i class="bi bi-check2-all status-read" title="Lu"></i>
                    </span>
                </asp:Panel>
            </div>
        </div>
    </ItemTemplate>
</asp:Repeater>

                    </div>

                    <div class="chat-footer">
                        <div class="message-input-container">
                            <div class="attachment-section" id="attachmentSection" style="display: none;">
                                <asp:FileUpload ID="fileUploadAttachment" runat="server" CssClass="form-control" />
                                <button type="button" onclick="cancelAttachment()" class="btn-cancel-attachment">✕</button>
                            </div>
                            <div class="emoji-picker" id="emojiPicker" style="display: none;">
                                <div class="emoji-grid">
                                    <span class="emoji-item" onclick="insertEmoji('😀')">😀</span>
                                    <span class="emoji-item" onclick="insertEmoji('😂')">😂</span>
                                    <span class="emoji-item" onclick="insertEmoji('😍')">😍</span>
                                    <span class="emoji-item" onclick="insertEmoji('🤔')">🤔</span>
                                    <span class="emoji-item" onclick="insertEmoji('👍')">👍</span>
                                    <span class="emoji-item" onclick="insertEmoji('👎')">👎</span>
                                    <span class="emoji-item" onclick="insertEmoji('❤️')">❤️</span>
                                    <span class="emoji-item" onclick="insertEmoji('🎉')">🎉</span>
                                    <span class="emoji-item" onclick="insertEmoji('🔥')">🔥</span>
                                    <span class="emoji-item" onclick="insertEmoji('💯')">💯</span>
                                    <span class="emoji-item" onclick="insertEmoji('😢')">😢</span>
                                    <span class="emoji-item" onclick="insertEmoji('😡')">😡</span>
                                </div>
                            </div>
                            <div class="input-row">
                                <button type="button" onclick="toggleAttachment()" class="btn-attachment" title="Joindre un fichier">📎</button>
                                <button type="button" onclick="toggleEmojiPicker()" class="btn-emoji" title="Émojis">😀</button>
                                <textarea rows="2" runat="server" id="txtMessage" placeholder="Écrivez votre message..." class="message-textarea"></textarea>
                                <button type="button" runat="server" id="btnenvoie" onserverclick="btnenvoie_ServerClick" class="btn-send">
                                    <i class="bi bi-send"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modal Création de Groupe -->
        <div id="createGroupModal" class="modal" style="display: none;">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Créer un nouveau groupe</h3>
                    <button class="modal-close" onclick="closeCreateGroupModal()">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="groupName">Nom du groupe</label>
                        <asp:TextBox ID="txtGroupName" runat="server" CssClass="form-control" placeholder="Entrez le nom du groupe"></asp:TextBox>
                    </div>
                    <div class="form-group">
                        <label>Sélectionner les membres</label>
                        <div class="member-selection">
                            <asp:CheckBoxList ID="chkMembersGroup" runat="server" CssClass="member-checkbox-list">
                            </asp:CheckBoxList>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="closeCreateGroupModal()">Annuler</button>
                    <asp:Button ID="btnCreateGroup" runat="server" Text="Créer le groupe" CssClass="btn btn-primary" OnClick="btnCreateGroup_Click" />
                </div>
            </div>
        </div>

        <!-- Modal Recherche Avancée -->
        <div id="searchModal" class="modal" style="display: none;">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Recherche dans les messages</h3>
                    <button class="modal-close" onclick="closeSearchModal()">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="searchText">Rechercher dans les messages</label>
                        <asp:TextBox ID="txtSearchMessages" runat="server" CssClass="form-control" placeholder="Tapez votre recherche..."></asp:TextBox>
                    </div>
                    <div class="form-group">
                        <label for="searchDate">Filtrer par date</label>
                        <asp:TextBox ID="txtSearchDate" runat="server" CssClass="form-control" TextMode="Date"></asp:TextBox>
                    </div>
                    <div class="search-results" id="searchResults">
                        <asp:ListView ID="listSearchResults" runat="server">
                            <EmptyDataTemplate>
                                <p>Aucun résultat trouvé</p>
                            </EmptyDataTemplate>
                            <ItemTemplate>
                                <div class="search-result-item">
                                    <div class="result-header">
                                        <strong><%# Eval("Expediteur") %></strong>
                                        <span class="result-date"><%# Eval("DateEnvoi", "{0:dd/MM/yyyy HH:mm}") %></span>
                                    </div>
                                    <div class="result-content"><%# Eval("Contenu") %></div>
                                </div>
                            </ItemTemplate>
                        </asp:ListView>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="closeSearchModal()">Fermer</button>
                    <asp:Button ID="btnSearch" runat="server" Text="Rechercher" CssClass="btn btn-primary" OnClick="btnSearch_Click" />
                </div>
            </div>
        </div>

    </main>

    <style>
        body {
            font-family: 'Segoe UI', sans-serif;
        }

        .chat-wrapper {
            display: flex;
            height: 80vh;
            border-radius: 12px;
            box-shadow: 0 0 15px rgba(0,0,0,0.1);
            overflow: hidden;
            background: #fff;
        }

        .contacts-panel {
            width: 280px;
            background: #f4f4f4;
            border-right: 1px solid #ddd;
            overflow-y: auto;
        }

        .contacts-header {
            background: #008374;
            color: #fff;
            padding: 15px;
            font-weight: bold;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header-tabs {
            display: flex;
            gap: 10px;
        }

        .tab-btn {
            background: transparent;
            border: 1px solid rgba(255,255,255,0.3);
            color: white;
            padding: 5px 12px;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 12px;
        }

        .tab-btn.active, .tab-btn:hover {
            background: rgba(255,255,255,0.2);
            border-color: rgba(255,255,255,0.5);
        }

        .btn-create-group {
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-create-group:hover {
            background: rgba(255,255,255,0.3);
            transform: scale(1.1);
        }

        .contact-item {
            padding: 12px;
            display: flex;
            align-items: center;
            gap: 12px;
            cursor: pointer;
            transition: background 0.3s;
        }

            .contact-item:hover {
                background: #e0f7f5;
            }

            .contact-item img {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                object-fit: cover;
            }

        .contact-name {
            font-weight: 500;
        }

        .chat-panel {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: #fafafa;
        }

        .chat-header {
            background: #fff;
            padding: 15px;
            border-bottom: 1px solid #eee;
            font-weight: bold;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header-left {
            flex: 1;
        }

        .header-actions {
            display: flex;
            gap: 10px;
        }

        .btn-header-action {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            color: #6c757d;
            width: 35px;
            height: 35px;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .btn-header-action:hover {
            background: #008374;
            color: white;
            transform: scale(1.1);
        }

        .chat-body {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .bubble {
            max-width: 60%;
            padding: 12px 16px;
            border-radius: 18px;
            font-size: 14px;
            position: relative;
        }

            .bubble.received {
                background: #eee;
                align-self: flex-start;
            }

            .bubble.sent {
                background: #008374;
                color: white;
                align-self: flex-end;
            }

        .chat-footer {
            padding: 12px 15px;
            background: #fff;
            border-top: 1px solid #eee;
        }

        .message-input-container {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .attachment-section {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 2px dashed #008374;
        }

        .btn-cancel-attachment {
            background: #dc3545;
            color: white;
            border: none;
            border-radius: 50%;
            width: 25px;
            height: 25px;
            font-size: 12px;
            cursor: pointer;
        }

        .input-row {
            display: flex;
            align-items: flex-end;
            gap: 10px;
        }

        .btn-attachment {
            background: #6c757d;
            color: white;
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-attachment:hover {
            background: #5a6268;
            transform: scale(1.1);
        }

        .btn-emoji {
            background: #ffc107;
            color: white;
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-emoji:hover {
            background: #e0a800;
            transform: scale(1.1);
        }

        /* Sélecteur d'émojis */
        .emoji-picker {
            position: absolute;
            bottom: 60px;
            left: 60px;
            background: white;
            border: 1px solid #ddd;
            border-radius: 10px;
            padding: 10px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 1000;
        }

        .emoji-grid {
            display: grid;
            grid-template-columns: repeat(6, 1fr);
            gap: 5px;
        }

        .emoji-item {
            font-size: 20px;
            padding: 5px;
            cursor: pointer;
            border-radius: 5px;
            transition: background 0.2s ease;
        }

        .emoji-item:hover {
            background: #f0f0f0;
        }

        .message-textarea {
            flex: 1;
            border-radius: 20px;
            padding: 12px 16px;
            border: 1px solid #ddd;
            resize: none;
            font-family: inherit;
            transition: border-color 0.3s ease;
        }

        .message-textarea:focus {
            outline: none;
            border-color: #008374;
            box-shadow: 0 0 0 2px rgba(0, 131, 116, 0.2);
        }

        .btn-send {
            background: #008374;
            color: #fff;
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-send:hover {
            background: #006b5d;
            transform: scale(1.1);
        }

        .btn-send:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }

        .online-dot {
            height: 10px;
            width: 10px;
            background-color: #28a745;
            border-radius: 50%;
            display: inline-block;
        }

        .contacts-search {
            padding: 10px;
            border-bottom: 1px solid #ddd;
        }

            .contacts-search input {
                width: 100%;
                padding: 8px;
                border-radius: 8px;
                border: 1px solid #ccc;
            }

        .contact-item {
            display: flex;
            align-items: center;
            padding: 10px;
            cursor: pointer;
            border-bottom: 1px solid #eee;
            position: relative;
        }

            .contact-item:hover {
                background-color: #e2f3f1;
            }

            .contact-item img {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                margin-right: 10px;
            }

        .notification-badge {
            position: absolute;
            right: 10px;
            top: 15px;
            background-color: #ff4b4b;
            color: white;
            padding: 2px 6px;
            font-size: 11px;
            border-radius: 10px;
        }
        .message-container {
    margin-bottom: 15px;
    max-width: 75%;
    padding: 10px;
    border-radius: 10px;
    background-color: #f1f1f1;
}

.sent {
    background-color: #d1f5e0;
    align-self: flex-end;
    margin-left: auto;
}

.received {
    background-color: #fff;
    border: 1px solid #ddd;
    margin-right: auto;
}

.message-header {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
}

.message-header .avatar {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    margin-right: 10px;
}

.message-body p {
    margin: 0;
    font-size: 14px;
}

.attachment-link {
    display: inline-block;
    margin-top: 5px;
    color: #008374;
    font-weight: bold;
    text-decoration: none;
    padding: 5px 10px;
    background: rgba(0, 131, 116, 0.1);
    border-radius: 15px;
    transition: all 0.3s ease;
}

.attachment-link:hover {
    background: rgba(0, 131, 116, 0.2);
    transform: translateY(-1px);
}

/* Indicateurs de statut de lecture */
.message-status {
    font-size: 11px;
    color: #6c757d;
    margin-top: 3px;
    text-align: right;
}

.status-sent {
    color: #6c757d;
}

.status-delivered {
    color: #28a745;
}

.status-read {
    color: #007bff;
}

/* Indicateur de frappe */
.typing-indicator {
    display: flex;
    align-items: center;
    padding: 10px 15px;
    font-style: italic;
    color: #6c757d;
    background: #f8f9fa;
    border-radius: 15px;
    margin: 5px 0;
}

.typing-dots {
    display: inline-flex;
    margin-left: 5px;
}

.typing-dots span {
    height: 4px;
    width: 4px;
    background: #6c757d;
    border-radius: 50%;
    display: inline-block;
    margin: 0 1px;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) { animation-delay: -0.32s; }
.typing-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
    0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
    40% { transform: scale(1); opacity: 1; }
}

/* Badge de messages non lus */
.unread-badge {
    background: #dc3545;
    color: white;
    border-radius: 50%;
    padding: 2px 6px;
    font-size: 10px;
    font-weight: bold;
    position: absolute;
    top: 5px;
    right: 5px;
    min-width: 18px;
    text-align: center;
}

/* Animation pour nouveaux messages */
@keyframes newMessage {
    0% { opacity: 0; transform: translateY(20px); }
    100% { opacity: 1; transform: translateY(0); }
}

.new-message {
    animation: newMessage 0.3s ease-out;
}

/* Amélioration du scroll */
.chat-body::-webkit-scrollbar {
    width: 6px;
}

.chat-body::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.chat-body::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.chat-body::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Styles pour les groupes */
.group-item {
    border-left: 3px solid #008374;
}

.group-avatar {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #008374, #00a693);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 18px;
}

.contact-info {
    flex: 1;
    margin-left: 10px;
}

.contact-status {
    font-size: 12px;
    color: #6c757d;
    margin-top: 2px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.contact-status.online {
    color: #28a745;
}

.contact-status.online::before {
    content: "●";
    color: #28a745;
    font-size: 8px;
}

.contact-status.offline {
    color: #6c757d;
}

.contact-status.offline::before {
    content: "●";
    color: #6c757d;
    font-size: 8px;
}

.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

.empty-state i {
    font-size: 48px;
    margin-bottom: 15px;
    opacity: 0.5;
}

.btn-create-first-group {
    background: #008374;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 20px;
    cursor: pointer;
    margin-top: 15px;
    transition: all 0.3s ease;
}

.btn-create-first-group:hover {
    background: #006b5d;
    transform: translateY(-2px);
}

/* Styles pour les modals */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 2000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: white;
    border-radius: 10px;
    width: 90%;
    max-width: 500px;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    color: #333;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #999;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close:hover {
    color: #333;
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    padding: 20px;
    border-top: 1px solid #eee;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    color: #333;
}

.form-control {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 14px;
}

.form-control:focus {
    outline: none;
    border-color: #008374;
    box-shadow: 0 0 0 2px rgba(0, 131, 116, 0.2);
}

.member-selection {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 10px;
}

.member-checkbox-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.member-checkbox-list li {
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
}

.member-checkbox-list li:last-child {
    border-bottom: none;
}

.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
}

.btn-primary {
    background: #008374;
    color: white;
}

.btn-primary:hover {
    background: #006b5d;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
}

/* Résultats de recherche */
.search-results {
    max-height: 300px;
    overflow-y: auto;
    margin-top: 15px;
}

.search-result-item {
    padding: 10px;
    border: 1px solid #eee;
    border-radius: 5px;
    margin-bottom: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.search-result-item:hover {
    background: #f8f9fa;
    border-color: #008374;
}

.result-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
}

.result-date {
    color: #6c757d;
    font-size: 12px;
}

.result-content {
    color: #333;
    font-size: 14px;
}

/* Notifications */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    z-index: 3000;
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-width: 300px;
    animation: slideInRight 0.3s ease-out;
}

.notification-success {
    border-left: 4px solid #28a745;
}

.notification-error {
    border-left: 4px solid #dc3545;
}

.notification-info {
    border-left: 4px solid #17a2b8;
}

.notification-content {
    display: flex;
    align-items: center;
    gap: 10px;
}

.notification-close {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: #999;
    padding: 0;
    margin-left: 15px;
}

.notification-close:hover {
    color: #333;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .chat-wrapper {
        flex-direction: column;
        height: 100vh;
    }

    .contacts-panel {
        width: 100%;
        height: 200px;
        border-right: none;
        border-bottom: 1px solid #ddd;
    }

    .chat-panel {
        flex: 1;
    }

    .modal-content {
        width: 95%;
        margin: 10px;
    }

    .header-tabs {
        flex-direction: column;
        gap: 5px;
    }

    .tab-btn {
        font-size: 11px;
        padding: 4px 8px;
    }
}

    </style>

    <script type="text/javascript">
        // Fonctionnalités avancées de messagerie
        let isTyping = false;
        let typingTimeout;

        // Gestion des pièces jointes
        function toggleAttachment() {
            const attachmentSection = document.getElementById('attachmentSection');
            if (attachmentSection.style.display === 'none') {
                attachmentSection.style.display = 'flex';
            } else {
                attachmentSection.style.display = 'none';
            }
        }

        function cancelAttachment() {
            document.getElementById('attachmentSection').style.display = 'none';
            document.getElementById('<%= fileUploadAttachment.ClientID %>').value = '';
        }

        // Auto-resize du textarea
        document.addEventListener('DOMContentLoaded', function() {
            const textarea = document.getElementById('<%= txtMessage.ClientID %>');
            if (textarea) {
                textarea.addEventListener('input', function() {
                    this.style.height = 'auto';
                    this.style.height = Math.min(this.scrollHeight, 120) + 'px';
                });

                // Envoi avec Ctrl+Enter
                textarea.addEventListener('keydown', function(e) {
                    if (e.ctrlKey && e.key === 'Enter') {
                        e.preventDefault();
                        document.getElementById('<%= btnenvoie.ClientID %>').click();
                    }
                });
            }

            // Auto-scroll vers le bas
            scrollToBottom();
        });

        // Scroll automatique vers le bas
        function scrollToBottom() {
            const chatBody = document.querySelector('.chat-body');
            if (chatBody) {
                chatBody.scrollTop = chatBody.scrollHeight;
            }
        }

        // Validation avant envoi
        function validateMessage() {
            const message = document.getElementById('<%= txtMessage.ClientID %>').value.trim();
            const fileUpload = document.getElementById('<%= fileUploadAttachment.ClientID %>');
            const hasFile = fileUpload && fileUpload.files.length > 0;

            if (!message && !hasFile) {
                alert('Veuillez saisir un message ou joindre un fichier.');
                return false;
            }

            // Vérification de la taille du fichier (5MB max)
            if (hasFile && fileUpload.files[0].size > 5 * 1024 * 1024) {
                alert('Le fichier ne peut pas dépasser 5MB.');
                return false;
            }

            return true;
        }

        // Gestion des onglets (Contacts/Groupes)
        function showTab(tabName) {
            // Masquer toutes les sections
            document.getElementById('contactsSection').style.display = 'none';
            document.getElementById('groupsSection').style.display = 'none';

            // Retirer la classe active de tous les boutons
            document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));

            // Afficher la section sélectionnée
            if (tabName === 'contacts') {
                document.getElementById('contactsSection').style.display = 'block';
                document.querySelector('.tab-btn[onclick="showTab(\'contacts\')"]').classList.add('active');
            } else if (tabName === 'groups') {
                document.getElementById('groupsSection').style.display = 'block';
                document.querySelector('.tab-btn[onclick="showTab(\'groups\')"]').classList.add('active');
            }
        }

        // Recherche de contacts et groupes
        function filterContacts() {
            const searchInput = document.getElementById('searchInput');
            const contacts = document.querySelectorAll('.contact-item');

            if (searchInput) {
                const searchTerm = searchInput.value.toLowerCase();
                contacts.forEach(contact => {
                    const contactName = contact.querySelector('.contact-name').textContent.toLowerCase();
                    const isVisible = contactName.includes(searchTerm);
                    contact.style.display = isVisible ? 'flex' : 'none';
                });
            }
        }

        // Gestion des modals
        function showCreateGroupModal() {
            console.log('Ouverture de la modal de création de groupe');
            const modal = document.getElementById('createGroupModal');
            if (modal) {
                modal.style.display = 'flex';
                // S'assurer que la modal reste visible
                setTimeout(() => {
                    modal.style.display = 'flex';
                }, 100);
            } else {
                console.error('Modal createGroupModal non trouvée');
            }
            return false; // Empêcher le postback
        }

        function closeCreateGroupModal() {
            console.log('Fermeture de la modal de création de groupe');
            const modal = document.getElementById('createGroupModal');
            if (modal) {
                modal.style.display = 'none';
            }
        }

        function showSearchModal() {
            document.getElementById('searchModal').style.display = 'flex';
        }

        function closeSearchModal() {
            document.getElementById('searchModal').style.display = 'none';
        }

        // Gestion des émojis
        function toggleEmojiPicker() {
            const emojiPicker = document.getElementById('emojiPicker');
            emojiPicker.style.display = emojiPicker.style.display === 'none' ? 'block' : 'none';
        }

        function insertEmoji(emoji) {
            const textarea = document.getElementById('<%= txtMessage.ClientID %>');
            if (textarea) {
                const cursorPos = textarea.selectionStart;
                const textBefore = textarea.value.substring(0, cursorPos);
                const textAfter = textarea.value.substring(cursorPos);
                textarea.value = textBefore + emoji + textAfter;
                textarea.focus();
                textarea.setSelectionRange(cursorPos + emoji.length, cursorPos + emoji.length);
            }
            toggleEmojiPicker();
        }

        // Notifications en temps réel (simulation)
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.innerHTML = `
                <div class="notification-content">
                    <i class="bi bi-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
                    <span>${message}</span>
                </div>
                <button class="notification-close" onclick="this.parentElement.remove()">&times;</button>
            `;

            document.body.appendChild(notification);

            // Auto-remove après 5 secondes
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 5000);
        }

        // Indicateur de frappe
        let typingTimer;
        function showTypingIndicator(show = true) {
            let indicator = document.getElementById('typingIndicator');
            if (show && !indicator) {
                indicator = document.createElement('div');
                indicator.id = 'typingIndicator';
                indicator.className = 'typing-indicator';
                indicator.innerHTML = `
                    <span>L'utilisateur est en train d'écrire</span>
                    <div class="typing-dots">
                        <span></span>
                        <span></span>
                        <span></span>
                    </div>
                `;
                document.querySelector('.chat-body').appendChild(indicator);
                scrollToBottom();
            } else if (!show && indicator) {
                indicator.remove();
            }
        }

        // Détection de frappe
        function handleTyping() {
            clearTimeout(typingTimer);
            // Simuler l'indicateur de frappe (en production, envoyer via SignalR)
            showTypingIndicator(true);

            typingTimer = setTimeout(() => {
                showTypingIndicator(false);
            }, 2000);
        }

        // Formatage du texte (gras, italique)
        function formatText(format) {
            const textarea = document.getElementById('<%= txtMessage.ClientID %>');
            if (textarea) {
                const start = textarea.selectionStart;
                const end = textarea.selectionEnd;
                const selectedText = textarea.value.substring(start, end);

                let formattedText = selectedText;
                switch(format) {
                    case 'bold':
                        formattedText = `**${selectedText}**`;
                        break;
                    case 'italic':
                        formattedText = `*${selectedText}*`;
                        break;
                    case 'code':
                        formattedText = `\`${selectedText}\``;
                        break;
                }

                textarea.value = textarea.value.substring(0, start) + formattedText + textarea.value.substring(end);
                textarea.focus();
            }
        }

        // Raccourcis clavier
        function handleKeyboardShortcuts(event) {
            if (event.ctrlKey) {
                switch(event.key) {
                    case 'b':
                        event.preventDefault();
                        formatText('bold');
                        break;
                    case 'i':
                        event.preventDefault();
                        formatText('italic');
                        break;
                    case 'k':
                        event.preventDefault();
                        formatText('code');
                        break;
                    case 'f':
                        event.preventDefault();
                        showSearchModal();
                        break;
                }
            }
        }

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            // Initialiser les onglets
            showTab('contacts');

            // Ajouter les événements
            const textarea = document.getElementById('<%= txtMessage.ClientID %>');
            if (textarea) {
                textarea.addEventListener('input', handleTyping);
                textarea.addEventListener('keydown', handleKeyboardShortcuts);
            }

            // Fermer les modals en cliquant à l'extérieur
            document.addEventListener('click', function(event) {
                if (event.target.classList.contains('modal')) {
                    event.target.style.display = 'none';
                }
            });

            // Fermer le sélecteur d'émojis en cliquant ailleurs
            document.addEventListener('click', function(event) {
                const emojiPicker = document.getElementById('emojiPicker');
                const emojiBtn = document.querySelector('.btn-emoji');
                if (emojiPicker && !emojiPicker.contains(event.target) && event.target !== emojiBtn) {
                    emojiPicker.style.display = 'none';
                }
            });

            // Marquer les messages comme lus quand on ouvre une conversation
            const chatBody = document.querySelector('.chat-body');
            if (chatBody) {
                chatBody.addEventListener('scroll', function() {
                    if (this.scrollTop + this.clientHeight >= this.scrollHeight - 10) {
                        // Marquer comme lu (implémenté côté serveur)
                    }
                });
            }
        });

        // Actualisation automatique des messages (polling simple)
        function startMessagePolling() {
            setInterval(function() {
                // Vérifier s'il y a de nouveaux messages
                // Cette fonction devrait faire un appel AJAX pour récupérer les nouveaux messages
                checkForNewMessages();
            }, 5000); // Vérifier toutes les 5 secondes
        }

        function checkForNewMessages() {
            // Implémentation AJAX pour vérifier les nouveaux messages
            // À développer selon les besoins
        }

        // Démarrer le polling au chargement de la page
        // startMessagePolling();

        // Configuration SignalR pour les notifications en temps réel
        var connection;
        var chatHub;
        var currentUserId = '<%= ide %>';
        var currentUserName = '<%= Session["NomComplet"] ?? "Utilisateur" %>';

        function initializeSignalR() {
            connection = new signalR.HubConnectionBuilder()
                .withUrl("/chatHub?userId=" + currentUserId)
                .build();

            // Événements de réception de messages
            connection.on("ReceiveMessage", function (fromUserId, message, senderName, senderPhoto, time) {
                console.log("Message reçu de " + fromUserId + ": " + message);

                // Vérifier si c'est la conversation active
                const currentChatId = document.getElementById('lblId').innerText;

                if (currentChatId === fromUserId) {
                    // Ajouter le message à la conversation active
                    addMessageToChat(message, senderName, senderPhoto, time || new Date().toLocaleTimeString(), false);
                    scrollToBottom();

                    // Marquer comme lu
                    markMessageAsRead(fromUserId);
                } else {
                    // Notification pour conversation non active
                    showNotification("Nouveau message de " + senderName, "info");
                    updateUnreadCount(fromUserId, 1);

                    // Jouer un son de notification
                    playNotificationSound();
                }
            });

            // Événements de groupe
            connection.on("ReceiveGroupMessage", function (fromUserId, message, senderName, senderPhoto, time) {
                if (document.getElementById('hdnIsGroup').value === "1") {
                    addMessageToChat(message, senderName, senderPhoto, time, false);
                    scrollToBottom();
                }
                showNotification("Nouveau message dans le groupe", "info");
            });

            // Indicateur de frappe
            connection.on("UserStartedTyping", function (userId, userName) {
                if (document.getElementById('lblId').innerText === userId) {
                    showTypingIndicator(true, userName);
                }
            });

            connection.on("UserStoppedTyping", function (userId) {
                if (document.getElementById('lblId').innerText === userId) {
                    showTypingIndicator(false);
                }
            });

            // Statut en ligne/hors ligne
            connection.on("UserConnected", function (userId) {
                updateUserStatus(userId, "En ligne");
            });

            connection.on("UserDisconnected", function (userId) {
                updateUserStatus(userId, "Hors ligne");
            });

            // Messages marqués comme lus
            connection.on("MessageMarkedAsRead", function (messageId, byUserId) {
                updateMessageReadStatus(messageId, true);
            });

            // Démarrer la connexion
            connection.start().then(function () {
                console.log("SignalR connecté");
                connection.invoke("GetOnlineUsers");
            }).catch(function (err) {
                console.error("Erreur SignalR: " + err.toString());
            });
        }

        // Fonctions utilitaires pour SignalR
        function addMessageToChat(message, senderName, senderPhoto, time, isSent) {
            const chatBody = document.querySelector('.chat-body');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message-container ${isSent ? 'sent' : 'received'} new-message`;

            messageDiv.innerHTML = `
                <div class="message-header">
                    <img class="avatar" src="../file/membr/${senderPhoto}" alt="Photo" onerror="this.src='../images/default-avatar.png'" />
                    <strong>${senderName}</strong>
                    <span class="date">${time}</span>
                </div>
                <div class="message-body">
                    <p>${message}</p>
                </div>
            `;

            chatBody.appendChild(messageDiv);
        }

        function updateUserStatus(userId, status) {
            const contacts = document.querySelectorAll('.contact-item');
            contacts.forEach(contact => {
                if (contact.querySelector('[CommandArgument="' + userId + '"]')) {
                    const statusElement = contact.querySelector('.contact-status');
                    if (statusElement) {
                        statusElement.textContent = status;
                        statusElement.className = `contact-status ${status === 'En ligne' ? 'online' : 'offline'}`;
                    }
                }
            });
        }

        function updateUnreadCount(userId, increment) {
            const contacts = document.querySelectorAll('.contact-item');
            contacts.forEach(contact => {
                if (contact.querySelector('[CommandArgument="' + userId + '"]')) {
                    const unreadElement = contact.querySelector('.unread-count');
                    if (unreadElement) {
                        let count = parseInt(unreadElement.textContent) || 0;
                        count += increment;
                        unreadElement.textContent = count;
                        unreadElement.style.display = count > 0 ? 'block' : 'none';
                    }
                }
            });
        }

        function updateMessageReadStatus(messageId, isRead) {
            const messageElements = document.querySelectorAll(`[data-message-id="${messageId}"]`);
            messageElements.forEach(element => {
                const statusIcon = element.querySelector('.status-indicator i');
                if (statusIcon && isRead) {
                    statusIcon.className = 'bi bi-check2-all status-read';
                }
            });
        }

        // Envoyer un message via SignalR
        function sendMessageViaSignalR(toUserId, message, isGroup = false) {
            if (connection && connection.state === signalR.HubConnectionState.Connected) {
                if (isGroup) {
                    connection.invoke("SendGroupMessage", toUserId, message, currentUserName, getCurrentUserPhoto());
                } else {
                    connection.invoke("SendMessage", toUserId, message, currentUserName, getCurrentUserPhoto());
                }
            }
        }

        function getCurrentUserPhoto() {
            // Récupérer la photo de l'utilisateur actuel
            return '<%= Session["PhotoProfil"] ?? "default-avatar.png" %>';
        }

        // Initialiser SignalR au chargement de la page
        document.addEventListener('DOMContentLoaded', function() {
            // Vérifier si SignalR est disponible
            if (typeof signalR !== 'undefined') {
                initializeSignalR();
            } else {
                console.warn("SignalR n'est pas disponible. Les notifications en temps réel sont désactivées.");
            }
        });
    </script>

    <!-- Références SignalR -->
    <script src="https://unpkg.com/@microsoft/signalr@latest/dist/browser/signalr.min.js"></script>


</asp:Content>
