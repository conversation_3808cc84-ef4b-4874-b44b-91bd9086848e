# Script PowerShell pour tester la messagerie entre utilisateurs A et B
# Exécuter depuis le dossier LinCom

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   Test Messagerie A → B - LinCom" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Vérifier la structure des dossiers
Write-Host "[1/6] Vérification de la structure des dossiers..." -ForegroundColor Yellow

$uploadDir = "file\messages"
if (-not (Test-Path $uploadDir)) {
    Write-Host "⚠️  Création du dossier $uploadDir" -ForegroundColor Yellow
    New-Item -ItemType Directory -Path $uploadDir -Force | Out-Null
    Write-Host "✅ Dossier créé" -ForegroundColor Green
} else {
    Write-Host "✅ Dossier $uploadDir existe" -ForegroundColor Green
}

# Vérifier les permissions
Write-Host ""
Write-Host "[2/6] Vérification des permissions..." -ForegroundColor Yellow

try {
    $testFile = Join-Path $uploadDir "test_permissions.txt"
    "Test de permissions" | Out-File -FilePath $testFile -Encoding UTF8
    
    if (Test-Path $testFile) {
        Remove-Item $testFile -Force
        Write-Host "✅ Permissions d'écriture OK" -ForegroundColor Green
    }
} catch {
    Write-Host "❌ Problème de permissions d'écriture" -ForegroundColor Red
    Write-Host "   Vérifiez les permissions du dossier $uploadDir" -ForegroundColor Yellow
}

# Créer des fichiers de test
Write-Host ""
Write-Host "[3/6] Création de fichiers de test..." -ForegroundColor Yellow

$testFiles = @(
    @{ Name = "test_document.pdf"; Content = "PDF de test"; Size = "1KB" },
    @{ Name = "test_image.jpg"; Content = "Image de test"; Size = "2KB" },
    @{ Name = "test_archive.zip"; Content = "Archive de test"; Size = "3KB" }
)

foreach ($file in $testFiles) {
    $filePath = Join-Path $uploadDir $file.Name
    $file.Content | Out-File -FilePath $filePath -Encoding UTF8
    
    if (Test-Path $filePath) {
        $fileInfo = Get-Item $filePath
        Write-Host "✅ $($file.Name) créé ($($fileInfo.Length) bytes)" -ForegroundColor Green
    } else {
        Write-Host "❌ Échec création $($file.Name)" -ForegroundColor Red
    }
}

# Vérifier les packages SignalR
Write-Host ""
Write-Host "[4/6] Vérification des packages SignalR..." -ForegroundColor Yellow

$packagesConfig = Get-Content "packages.config" -ErrorAction SilentlyContinue
if ($packagesConfig -match "Microsoft.AspNet.SignalR" -and $packagesConfig -match "Microsoft.Owin") {
    Write-Host "✅ Packages SignalR trouvés dans packages.config" -ForegroundColor Green
} else {
    Write-Host "⚠️  Packages SignalR non trouvés, vérifiez l'installation" -ForegroundColor Yellow
}

# Vérifier les redirections d'assemblies
Write-Host ""
Write-Host "[5/6] Vérification des redirections d'assemblies..." -ForegroundColor Yellow

$webConfig = Get-Content "Web.config" -Raw -ErrorAction SilentlyContinue
if ($webConfig -match "Microsoft\.Owin.*4\.2\.2\.0" -and $webConfig -match "Microsoft\.AspNet\.SignalR\.Core.*2\.4\.3\.0") {
    Write-Host "✅ Redirections d'assemblies trouvées dans Web.config" -ForegroundColor Green
} else {
    Write-Host "⚠️  Redirections d'assemblies non trouvées, vérifiez Web.config" -ForegroundColor Yellow
}

# Vérifier les corrections
Write-Host ""
Write-Host "[6/6] Vérification des corrections..." -ForegroundColor Yellow

$messageriePage = Get-Content "messagerie.aspx" -Raw -ErrorAction SilentlyContinue
$messagerieCs = Get-Content "messagerie.aspx.cs" -Raw -ErrorAction SilentlyContinue

$corrections = @(
    @{ Name = "Bouton type='button'"; Pattern = "type=""button"".*showCreateGroupModal"; Found = $false },
    @{ Name = "return false dans onclick"; Pattern = "showCreateGroupModal\(\).*return false"; Found = $false },
    @{ Name = "Fonction NotifierNouveauMessage"; Pattern = "NotifierNouveauMessage.*destinataireId"; Found = $false },
    @{ Name = "Gestion SignalR améliorée"; Pattern = "connection\.on.*ReceiveMessage"; Found = $false }
)

foreach ($correction in $corrections) {
    if ($messageriePage -match $correction.Pattern -or $messagerieCs -match $correction.Pattern) {
        $correction.Found = $true
        Write-Host "✅ Correction trouvée: $($correction.Name)" -ForegroundColor Green
    } else {
        Write-Host "⚠️  Correction non trouvée: $($correction.Name)" -ForegroundColor Yellow
    }
}

# Résumé et instructions
Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "           RÉSUMÉ DU TEST" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

Write-Host ""
Write-Host "📁 Structure des dossiers: OK" -ForegroundColor Green
Write-Host "🔐 Permissions: OK" -ForegroundColor Green
Write-Host "📎 Fichiers de test: Créés" -ForegroundColor Green
Write-Host "📦 Packages SignalR: Vérifiés" -ForegroundColor Green
Write-Host "⚙️  Redirections d'assemblies: Vérifiées" -ForegroundColor Green
Write-Host "🔧 Corrections: Vérifiées" -ForegroundColor Green

Write-Host ""
Write-Host "🧪 INSTRUCTIONS DE TEST:" -ForegroundColor Cyan
Write-Host ""
Write-Host "1. Ouvrir Visual Studio 2022" -ForegroundColor White
Write-Host "2. Ouvrir LinCom.sln" -ForegroundColor White
Write-Host "3. Build → Rebuild Solution" -ForegroundColor White
Write-Host "4. F5 pour démarrer l'application" -ForegroundColor White
Write-Host "5. Noter l'URL (ex: https://localhost:44319/)" -ForegroundColor White
Write-Host ""
Write-Host "6. Ouvrir deux navigateurs différents:" -ForegroundColor White
Write-Host "   - Navigateur 1: Utilisateur A" -ForegroundColor White
Write-Host "   - Navigateur 2: Utilisateur B (mode incognito)" -ForegroundColor White
Write-Host ""
Write-Host "7. Dans chaque navigateur:" -ForegroundColor White
Write-Host "   - Aller sur /messagerie.aspx" -ForegroundColor White
Write-Host "   - Vérifier 'SignalR connecté' dans la console (F12)" -ForegroundColor White
Write-Host ""
Write-Host "8. Test Messages Texte:" -ForegroundColor White
Write-Host "   - A sélectionne B et envoie un message" -ForegroundColor White
Write-Host "   - Vérifier que B reçoit instantanément" -ForegroundColor White
Write-Host ""
Write-Host "9. Test Pièces Jointes:" -ForegroundColor White
Write-Host "   - A envoie un message avec pièce jointe" -ForegroundColor White
Write-Host "   - B vérifie réception et téléchargement" -ForegroundColor White
Write-Host ""
Write-Host "10. Test Bidirectionnel:" -ForegroundColor White
Write-Host "    - B répond à A avec texte et pièce jointe" -ForegroundColor White
Write-Host "    - A vérifie réception" -ForegroundColor White
Write-Host ""
Write-Host "📋 FICHIERS DE TEST DISPONIBLES:" -ForegroundColor Cyan
foreach ($file in $testFiles) {
    Write-Host "   📄 $($file.Name)" -ForegroundColor White
}

Write-Host ""
Write-Host "📚 DOCUMENTATION DÉTAILLÉE:" -ForegroundColor Cyan
Write-Host "   📄 TEST_MESSAGERIE_A_VERS_B.md" -ForegroundColor White
Write-Host "   📄 CORRECTION_MESSAGES_ET_GROUPES.md" -ForegroundColor White

Write-Host ""
Write-Host "🎯 Si tous les tests passent, LinCom est prêt pour la production !" -ForegroundColor Green
Write-Host ""

# Pause pour permettre la lecture
Read-Host "Appuyez sur Entrée pour continuer..."
