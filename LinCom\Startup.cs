using Microsoft.AspNet.SignalR;
using Microsoft.Owin;
using Owin;

[assembly: OwinStartup(typeof(LinCom.Startup))]

namespace LinCom
{
    public class Startup
    {
        public void Configuration(IAppBuilder app)
        {
            // Configuration de SignalR
            var hubConfiguration = new HubConfiguration()
            {
                EnableDetailedErrors = true,
                EnableJSONP = true
            };

            app.MapSignalR(hubConfiguration);
        }
    }
}
