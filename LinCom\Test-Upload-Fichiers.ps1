# Script PowerShell pour tester la fonctionnalité d'upload de fichiers
# Exécuter depuis le dossier LinCom

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   Test Upload de Fichiers - LinCom" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Vérifier la structure des dossiers
Write-Host "[1/5] Vérification de la structure des dossiers..." -ForegroundColor Yellow

$uploadDir = "file\messages"
if (-not (Test-Path $uploadDir)) {
    Write-Host "⚠️  Création du dossier $uploadDir" -ForegroundColor Yellow
    New-Item -ItemType Directory -Path $uploadDir -Force | Out-Null
    Write-Host "✅ Dossier créé" -ForegroundColor Green
} else {
    Write-Host "✅ Dossier $uploadDir existe" -ForegroundColor Green
}

# Vérifier les permissions
Write-Host ""
Write-Host "[2/5] Vérification des permissions..." -ForegroundColor Yellow

try {
    $testFile = Join-Path $uploadDir "test_permissions.txt"
    "Test de permissions" | Out-File -FilePath $testFile -Encoding UTF8
    
    if (Test-Path $testFile) {
        Remove-Item $testFile -Force
        Write-Host "✅ Permissions d'écriture OK" -ForegroundColor Green
    }
} catch {
    Write-Host "❌ Problème de permissions d'écriture" -ForegroundColor Red
    Write-Host "   Vérifiez les permissions du dossier $uploadDir" -ForegroundColor Yellow
}

# Créer des fichiers de test
Write-Host ""
Write-Host "[3/5] Création de fichiers de test..." -ForegroundColor Yellow

$testFiles = @(
    @{ Name = "test_document.pdf"; Content = "PDF de test"; Size = "1KB" },
    @{ Name = "test_image.jpg"; Content = "Image de test"; Size = "2KB" },
    @{ Name = "test_texte.txt"; Content = "Document texte de test pour LinCom"; Size = "500B" }
)

foreach ($file in $testFiles) {
    $filePath = Join-Path $uploadDir $file.Name
    $file.Content | Out-File -FilePath $filePath -Encoding UTF8
    
    if (Test-Path $filePath) {
        $fileInfo = Get-Item $filePath
        Write-Host "✅ $($file.Name) créé ($($fileInfo.Length) bytes)" -ForegroundColor Green
    } else {
        Write-Host "❌ Échec création $($file.Name)" -ForegroundColor Red
    }
}

# Tester la résolution d'URLs
Write-Host ""
Write-Host "[4/5] Test de résolution d'URLs..." -ForegroundColor Yellow

$testUrls = @(
    "file/messages/test_document.pdf",
    "~/file/messages/test_image.jpg",
    "test_texte.txt"
)

foreach ($url in $testUrls) {
    Write-Host "📎 URL de test: $url" -ForegroundColor Cyan
    
    # Simuler la résolution d'URL
    if ($url.StartsWith("~/")) {
        $resolved = $url.Substring(2)  # Enlever ~/
    } elseif ($url.StartsWith("file/")) {
        $resolved = $url
    } else {
        $resolved = "file/messages/$url"
    }
    
    Write-Host "   → Résolu en: $resolved" -ForegroundColor White
    
    # Vérifier que le fichier existe
    $physicalPath = $resolved.Replace("/", "\")
    if (Test-Path $physicalPath) {
        Write-Host "   ✅ Fichier accessible" -ForegroundColor Green
    } else {
        Write-Host "   ❌ Fichier non trouvé" -ForegroundColor Red
    }
}

# Tester les types de fichiers autorisés
Write-Host ""
Write-Host "[5/5] Test des validations..." -ForegroundColor Yellow

$validExtensions = @(".pdf", ".doc", ".docx", ".txt", ".jpg", ".jpeg", ".png", ".gif", ".zip", ".rar")
$testExtensions = @(".pdf", ".jpg", ".txt", ".exe", ".bat", ".js")

Write-Host "Extensions autorisées: $($validExtensions -join ', ')" -ForegroundColor Cyan

foreach ($ext in $testExtensions) {
    if ($validExtensions -contains $ext) {
        Write-Host "✅ $ext - Autorisé" -ForegroundColor Green
    } else {
        Write-Host "❌ $ext - Bloqué (sécurité)" -ForegroundColor Red
    }
}

# Tester la taille maximale
Write-Host ""
Write-Host "Taille maximale autorisée: 5MB" -ForegroundColor Cyan
$testSizes = @("1KB", "500KB", "2MB", "6MB", "10MB")

foreach ($size in $testSizes) {
    $sizeBytes = switch ($size) {
        "1KB" { 1024 }
        "500KB" { 500 * 1024 }
        "2MB" { 2 * 1024 * 1024 }
        "6MB" { 6 * 1024 * 1024 }
        "10MB" { 10 * 1024 * 1024 }
    }
    
    if ($sizeBytes -le (5 * 1024 * 1024)) {
        Write-Host "✅ $size - Autorisé" -ForegroundColor Green
    } else {
        Write-Host "❌ $size - Trop volumineux" -ForegroundColor Red
    }
}

# Résumé et instructions
Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "           RÉSUMÉ DU TEST" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

Write-Host ""
Write-Host "📁 Structure des dossiers: OK" -ForegroundColor Green
Write-Host "🔐 Permissions: OK" -ForegroundColor Green
Write-Host "📎 Fichiers de test: Créés" -ForegroundColor Green
Write-Host "🔗 Résolution d'URLs: Testée" -ForegroundColor Green
Write-Host "🛡️  Validations: Configurées" -ForegroundColor Green

Write-Host ""
Write-Host "🧪 TESTS À EFFECTUER DANS L'APPLICATION:" -ForegroundColor Cyan
Write-Host ""
Write-Host "1. Démarrer LinCom (F5 dans Visual Studio)" -ForegroundColor White
Write-Host "2. Aller sur /messagerie.aspx" -ForegroundColor White
Write-Host "3. Sélectionner un contact" -ForegroundColor White
Write-Host "4. Cliquer sur le bouton 📎 (Joindre un fichier)" -ForegroundColor White
Write-Host "5. Sélectionner un fichier de test créé" -ForegroundColor White
Write-Host "6. Envoyer le message" -ForegroundColor White
Write-Host "7. Vérifier que la pièce jointe s'affiche" -ForegroundColor White
Write-Host "8. Cliquer sur le lien pour télécharger" -ForegroundColor White
Write-Host "9. Vérifier qu'aucune erreur 404 n'apparaît" -ForegroundColor White

Write-Host ""
Write-Host "📋 FICHIERS DE TEST DISPONIBLES:" -ForegroundColor Cyan
foreach ($file in $testFiles) {
    Write-Host "   📄 $($file.Name)" -ForegroundColor White
}

Write-Host ""
Write-Host "🎯 Si tout fonctionne, l'upload est opérationnel !" -ForegroundColor Green
Write-Host ""

# Nettoyer les fichiers de test (optionnel)
$cleanup = Read-Host "Supprimer les fichiers de test? (y/N)"
if ($cleanup -eq "y" -or $cleanup -eq "Y") {
    foreach ($file in $testFiles) {
        $filePath = Join-Path $uploadDir $file.Name
        if (Test-Path $filePath) {
            Remove-Item $filePath -Force
            Write-Host "🗑️  $($file.Name) supprimé" -ForegroundColor Yellow
        }
    }
    Write-Host "✅ Nettoyage terminé" -ForegroundColor Green
} else {
    Write-Host "📁 Fichiers de test conservés pour les tests" -ForegroundColor Cyan
}

Write-Host ""
Write-Host "Test terminé ! 🚀" -ForegroundColor Green
