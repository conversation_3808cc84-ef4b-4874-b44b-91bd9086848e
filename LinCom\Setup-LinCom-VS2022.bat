@echo off
echo ========================================
echo    LinCom Setup pour Visual Studio 2022
echo ========================================
echo.

echo [1/5] Verification de l'environnement...
where dotnet >nul 2>nul
if %errorlevel% neq 0 (
    echo ERREUR: .NET Framework non trouve. Veuillez installer Visual Studio 2022.
    pause
    exit /b 1
)
echo ✓ .NET Framework detecte

echo.
echo [2/5] Verification de la structure du projet...
if not exist "LinCom.sln" (
    echo ERREUR: LinCom.sln non trouve. Executez ce script depuis le dossier racine.
    pause
    exit /b 1
)
echo ✓ Solution LinCom trouvee

echo.
echo [3/5] Creation des dossiers necessaires...
if not exist "file\messages" mkdir "file\messages"
if not exist "images" mkdir "images"
if not exist "Hubs" mkdir "Hubs"
echo ✓ Dossiers crees

echo.
echo [4/5] Verification des fichiers critiques...
set MISSING_FILES=0

if not exist "Startup.cs" (
    echo ⚠ Startup.cs manquant
    set MISSING_FILES=1
)

if not exist "Hubs\ChatHub.cs" (
    echo ⚠ ChatHub.cs manquant
    set MISSING_FILES=1
)

if not exist "messagerie.aspx" (
    echo ⚠ messagerie.aspx manquant
    set MISSING_FILES=1
)

if %MISSING_FILES% equ 1 (
    echo.
    echo ATTENTION: Certains fichiers sont manquants.
    echo Veuillez vous assurer que tous les fichiers ont ete correctement copies.
    echo.
) else (
    echo ✓ Tous les fichiers critiques sont presents
)

echo.
echo [5/5] Instructions finales...
echo.
echo ========================================
echo    PROCHAINES ETAPES:
echo ========================================
echo.
echo 1. Ouvrir Visual Studio 2022
echo 2. Ouvrir LinCom.sln
echo 3. Clic droit sur la solution ^> "Restore NuGet Packages"
echo 4. Dans Package Manager Console, executer:
echo    Install-Package Microsoft.AspNet.SignalR -Version 2.4.3
echo 5. Build ^> Rebuild Solution
echo 6. Appuyer F5 pour demarrer
echo.
echo ========================================
echo    DOCUMENTATION DISPONIBLE:
echo ========================================
echo.
echo - README_PROJET_COMPLET.md
echo - INSTALLATION_VISUAL_STUDIO_2022.md
echo - VERIFICATION_INTEGRATION_VS2022.md
echo.
echo Setup termine! Bon developpement! 🚀
echo.
pause
