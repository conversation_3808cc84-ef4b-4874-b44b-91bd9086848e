# 🚀 Guide d'Installation - LinCom avec Visual Studio 2022

## 📋 Prérequis

### ✅ **Logiciels Requis**
- **Visual Studio 2022** (Community, Professional ou Enterprise)
- **.NET Framework 4.8** (inclus avec VS 2022)
- **SQL Server** (LocalDB ou version complète)
- **IIS Express** (inclus avec VS 2022)

### ✅ **Workloads Visual Studio Requis**
- **ASP.NET and web development**
- **Data storage and processing** (pour SQL Server)

## 🔧 Étapes d'Installation

### **1. Ouvrir le Projet**
1. Ouvrir **Visual Studio 2022**
2. <PERSON><PERSON><PERSON> sur **"Open a project or solution"**
3. Naviguer vers le dossier `LinCom`
4. Sélectionner le fichier **`LinCom.sln`**
5. Cliquer **"Open"**

### **2. Restaurer les Packages NuGet**
1. **Clic droit** sur la solution dans l'**Explorateur de solutions**
2. <PERSON><PERSON>lection<PERSON> **"Restore NuGet Packages"**
3. Attendre la fin du téléchargement

**OU** utiliser la **Console du Gestionnaire de Package** :
```powershell
Update-Package -Reinstall
```

### **3. Installer les Packages SignalR**
Ouvrir la **Console du Gestionnaire de Package** :
- **Outils** → **Gestionnaire de Package NuGet** → **Console du Gestionnaire de Package**

Exécuter les commandes suivantes :
```powershell
Install-Package Microsoft.AspNet.SignalR -Version 2.4.3
Install-Package Microsoft.AspNet.SignalR.Core -Version 2.4.3
Install-Package Microsoft.AspNet.SignalR.SystemWeb -Version 2.4.3
Install-Package Microsoft.Owin -Version 4.2.2
Install-Package Microsoft.Owin.Host.SystemWeb -Version 4.2.2
Install-Package Microsoft.Owin.Security -Version 4.2.2
Install-Package Owin -Version 1.0
```

**OU** exécuter le script PowerShell fourni :
```powershell
.\InstallSignalRPackages.ps1
```

### **4. Configurer la Base de Données**
1. Ouvrir **SQL Server Management Studio** ou **Visual Studio SQL Server Object Explorer**
2. Se connecter à **(localdb)\MSSQLLocalDB** ou votre instance SQL Server
3. Exécuter le script **`restor.sql`** pour créer la base de données
4. Vérifier que la chaîne de connexion dans **`Web.config`** est correcte

### **5. Vérifier les Références**
Dans l'**Explorateur de solutions**, vérifier que toutes les références sont correctes :
- ✅ **EntityFramework** (6.5.1)
- ✅ **Microsoft.AspNet.SignalR.Core** (2.4.3)
- ✅ **Microsoft.AspNet.SignalR.SystemWeb** (2.4.3)
- ✅ **Microsoft.Owin** (4.2.2)
- ✅ **BCrypt.Net-Next** (4.0.3)

### **6. Compiler le Projet**
1. **Build** → **Rebuild Solution** (Ctrl+Shift+B)
2. Vérifier qu'il n'y a **aucune erreur** de compilation
3. Résoudre les éventuelles erreurs de références manquantes

### **7. Configurer IIS Express**
1. **Clic droit** sur le projet **LinCom**
2. Sélectionner **"Properties"**
3. Onglet **"Web"**
4. Vérifier que **"Use IIS Express"** est coché
5. Noter l'**URL du projet** (ex: https://localhost:44319/)

## 🚀 Lancement du Projet

### **Démarrage Standard**
1. Appuyer sur **F5** ou cliquer sur **"Start Debugging"**
2. Le navigateur s'ouvrira automatiquement
3. Naviguer vers **`/messagerie.aspx`** pour tester la messagerie

### **Démarrage sans Débogage**
1. Appuyer sur **Ctrl+F5** ou **"Start Without Debugging"**
2. Plus rapide pour les tests

## 🧪 Tests de Fonctionnement

### **1. Test de Base**
- ✅ La page d'accueil se charge
- ✅ Pas d'erreurs dans la console développeur
- ✅ Les styles CSS sont appliqués

### **2. Test de la Messagerie**
- ✅ Accéder à `/messagerie.aspx`
- ✅ L'interface se charge correctement
- ✅ Les onglets Contacts/Groupes fonctionnent
- ✅ La console affiche "SignalR connecté"

### **3. Test SignalR**
- ✅ Ouvrir la console développeur (F12)
- ✅ Vérifier les messages SignalR
- ✅ Pas d'erreurs de connexion

## 🔧 Résolution de Problèmes

### **Erreur : "Could not load file or assembly"**
**Solution :**
1. **Clic droit** sur **References** → **Manage NuGet Packages**
2. Onglet **"Updates"** → **Update All**
3. Rebuild la solution

### **Erreur : "SignalR Hub not found"**
**Solution :**
1. Vérifier que **`Startup.cs`** est présent
2. Vérifier que **`ChatHub.cs`** est dans le dossier **`Hubs/`**
3. Rebuild la solution

### **Erreur de Base de Données**
**Solution :**
1. Vérifier la chaîne de connexion dans **`Web.config`**
2. S'assurer que SQL Server est démarré
3. Recréer la base de données avec **`restor.sql`**

### **Erreur : "Package not found"**
**Solution :**
1. **Outils** → **Options** → **NuGet Package Manager**
2. Vérifier les **Package Sources**
3. Ajouter **https://api.nuget.org/v3/index.json** si manquant

## 📁 Structure des Fichiers Ajoutés

```
LinCom/
├── Hubs/
│   └── ChatHub.cs                    # Hub SignalR
├── Startup.cs                        # Configuration OWIN
├── file/messages/                    # Dossier pièces jointes
│   └── readme.txt
├── images/
│   └── default-avatar.png           # Avatar par défaut
├── MESSAGERIE_AMELIORATIONS.md      # Documentation
├── FONCTIONNALITES_AVANCEES_RAPPORT.md
├── GUIDE_MESSAGERIE_DEV.md
├── GUIDE_TESTS_FONCTIONNALITES_AVANCEES.md
└── InstallSignalRPackages.ps1       # Script d'installation
```

## ✅ Checklist de Validation

- [ ] Solution ouverte dans Visual Studio 2022
- [ ] Packages NuGet restaurés
- [ ] Packages SignalR installés
- [ ] Base de données configurée
- [ ] Compilation sans erreur
- [ ] IIS Express configuré
- [ ] Application démarre correctement
- [ ] Messagerie fonctionnelle
- [ ] SignalR connecté

## 🎯 Prêt pour le Développement !

Une fois toutes ces étapes complétées, votre environnement LinCom est prêt avec toutes les fonctionnalités avancées de messagerie !

Pour plus d'informations, consultez les autres fichiers de documentation inclus dans le projet.
