using LinCom.Class;
using LinCom.Classe;
using LinCom.Imp;
using LinCom.Model;
using System;
using System.Collections.Generic;
using System.Diagnostics.Eventing.Reader;
using System.IO;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace LinCom
{
    public partial class messagerie : System.Web.UI.Page
    {
        IMessage objmes = new MessageImp();
        Message_Class mess = new Message_Class();
        IConversation objconver = new ConversationImp();
        Conversation_Class conver = new Conversation_Class();
        ParticipantConversation_Class partconver = new ParticipantConversation_Class();
        MessageStatus_Class messtatu = new MessageStatus_Class();

        Organisation_Class org = new Organisation_Class();
        Organisation_Class orga = new Organisation_Class();
        IOrganisation objorg = new OrganisationImp();
        IPoste objpost = new PosteImp();
        Post_Class post = new Post_Class();
        Post_Class pos = new Post_Class();
        IDomainePost objdompost = new DomainePostImp();
        DomainePost_Class dompost = new DomainePost_Class();
        Membre_Class mem = new Membre_Class();
        IMembre objmem = new MembreImp();
        IPartenaire objpart = new PartenaireImp();
        Partenaire_Class part = new Partenaire_Class();
        DomaineIntervention_Class actdom = new DomaineIntervention_Class();
        IDomaineIntervention objactdom = new DomaineInterventionImp();
        ICommonCode co = new CommonCode();
        //   UrlPartage = $"{Request.Url.GetLeftPart(UriPartial.Authority)}/post/" + ep.name,
        IDomaineInterventionOrganisation objdomorg = new DomaineInterventionOrganisationImp();
        DomaineInterventionOrganisation_Class domorg = new DomaineInterventionOrganisation_Class();
        int info;
        static string imge, imge1, pdfe, nameorg;
        protected long ide; static long idorg;
        static int rolid;
        long index;
        static long conversationreceveur;

        // Déclarations des contrôles ASP.NET pour les nouvelles fonctionnalités
        protected System.Web.UI.WebControls.CheckBoxList chkMembersGroup;
        protected System.Web.UI.WebControls.FileUpload fileUploadAttachment;
        protected System.Web.UI.WebControls.ListView listGroupes;
        protected System.Web.UI.WebControls.ListView listSearchResults;
        protected System.Web.UI.WebControls.TextBox txtGroupName;
        protected System.Web.UI.WebControls.TextBox txtSearchDate;
        protected System.Web.UI.WebControls.TextBox txtSearchMessages;
        protected System.Web.UI.WebControls.Button btnCreateGroup;
        protected System.Web.UI.WebControls.Button btnSearch;

        protected void Page_Load(object sender, EventArgs e)
        {
            HttpCookie role = Request.Cookies["role"];
            HttpCookie usernm = Request.Cookies["usernm"];
            HttpCookie idperso = Request.Cookies["iduser"];

            if (Request.Cookies["iduser"] != null && Request.Cookies["role"] != null)
            {//admin
                long.TryParse(Request.Cookies["iduser"].Value, out ide);//idconnecte

            }
            if (!IsPostBack)
            {
                // Par défaut, tu peux initialiser avec une conversation
                
                ChargerMessages();
                AppelMethode();
            }
        }
        public void AppelMethode()
        {
            objmem.ChargerListview(listmembre,-1,"actif","");
            ChargerGroupes();
            ChargerMembresGroupe();
        }

        private void ChargerGroupes()
        {
            // Charger les groupes où l'utilisateur est participant
            objconver.ChargerGroupesUtilisateur(listGroupes, ide);
        }

        private void ChargerMembresGroupe()
        {
            // Charger tous les membres pour la création de groupes
            objmem.ChargerCheckBoxList(chkMembersGroup, -1, "actif", "");
        }
     

        protected void listmembre_ItemCommand(object sender, ListViewCommandEventArgs e)
        {
            if (e.CommandName == "viewmem")
            {
                long idMembre =Convert.ToInt64( e.CommandArgument.ToString());
                // Utilisez l'ID pour récupérer les détails du membre
                objmem.AfficherDetails(idMembre,mem);

                // Changez le titre de la discussion
                lblHeader.Text = "Message envoyé à " + mem.Nom+" "+mem.Prenom;
                lblId.Text = mem.MembreId.ToString();
                hdnIsGroup.Value = "0"; // Conversation privée

                ChargerMessages();
            }
        }

        protected void listGroupes_ItemCommand(object sender, ListViewCommandEventArgs e)
        {
            if (e.CommandName == "viewgroup")
            {
                long conversationId = Convert.ToInt64(e.CommandArgument.ToString());

                // Récupérer les détails du groupe
                objconver.AfficherDetails(conversationId, conver);

                // Changez le titre de la discussion
                lblHeader.Text = "Groupe: " + conver.Sujet;
                lblId.Text = conversationId.ToString();
                hdnIsGroup.Value = "1"; // Conversation de groupe

                ChargerMessagesGroupe(conversationId);
            }
        }

        protected void btnCreateGroup_Click(object sender, EventArgs e)
        {
            if (!string.IsNullOrWhiteSpace(txtGroupName.Text))
            {
                try
                {
                    // Créer la conversation de groupe
                    conver.Sujet = txtGroupName.Text.Trim();
                    conver.IsGroup = 1;
                    conver.CreatedAt = DateTime.Now;

                    int result = objconver.Creer(conver);

                    if (result == 1)
                    {
                        // Récupérer l'ID de la conversation créée
                        long groupId = objconver.ObtenirDerniereConversation(ide);

                        // Ajouter le créateur comme participant
                        objconver.AjouterParticipant(groupId, ide);

                        // Ajouter les membres sélectionnés
                        foreach (ListItem item in chkMembersGroup.Items)
                        {
                            if (item.Selected)
                            {
                                long membreId = Convert.ToInt64(item.Value);
                                objconver.AjouterParticipant(groupId, membreId);
                            }
                        }

                        // Recharger les groupes
                        ChargerGroupes();

                        // Vider le formulaire
                        txtGroupName.Text = "";
                        chkMembersGroup.ClearSelection();

                        Response.Write("<script>closeCreateGroupModal(); showNotification('Groupe créé avec succès!', 'success');</script>");
                    }
                    else
                    {
                        Response.Write("<script>showNotification('Erreur lors de la création du groupe.', 'error');</script>");
                    }
                }
                catch (Exception ex)
                {
                    Response.Write("<script>showNotification('Erreur: " + ex.Message.Replace("'", "\\'") + "', 'error');</script>");
                }
            }
            else
            {
                Response.Write("<script>showNotification('Veuillez saisir un nom pour le groupe.', 'error');</script>");
            }
        }

    private void CreationConversation(int cd,string sujetgroup)
        {//creation d'une nouvelle convrsation

            if (cd==0)
            {//privee
                conver.Sujet = "";
                conver.IsGroup = 0;
                conver.CreatedAt = DateTime.Now;
                objconver.Creer(conver);
            }
            else if (cd==1)
            {
                //equipe
                conver.Sujet = sujetgroup;
                conver.IsGroup = 1;
                conver.CreatedAt = DateTime.Now;
                objconver.Creer(conver);
            }
              
        }
      private  void CreationParticipantConversation(long memparticipant)
        {//creation des membres qui commencent le tchat
         //long conversationreceveur = objconver.VerifierConversationId(ide, Convert.ToInt64(memparticipant));
         //if (conversationreceveur > 0)
         //{
         //    partconver.ConversationId = conversationreceveur;
         //    partconver.MembreId= memparticipant;
         //    partconver.JoinedAt = DateTime.Now;

            //    objconver.AjouterParticipant(conversationreceveur, Convert.ToInt64(memparticipant));

            //}
            conversationreceveur = objconver.VerifierConversationId(ide, memparticipant);

            // Si aucune conversation => on la crée
            if (conversationreceveur <= 0)
            {
                CreationConversation(0, ""); // conversation privée
                conversationreceveur = objconver.VerifierConversationId(ide, memparticipant);
            }
            else
            {
                partconver.ConversationId = conversationreceveur;
                partconver.MembreId = memparticipant;
                partconver.JoinedAt = DateTime.Now;
                // Ensuite on ajoute les 2 participants S'ILS NE SONT PAS DÉJÀ DEDANS

                if (!objconver.ParticipantExiste(conversationreceveur, ide))
                    objconver.AjouterParticipant(conversationreceveur, ide);

                if (!objconver.ParticipantExiste(conversationreceveur, memparticipant))
                    objconver.AjouterParticipant(conversationreceveur, memparticipant);

            }

        }
        private int CreationMessage(long convID,long membrId)
        {
            mess.ConversationId = convID;
            mess.SenderId = membrId;
            mess.Contenu = txtMessage.Value;
            mess.DateEnvoi = DateTime.Now;
            mess.name = "";
            mess.AttachmentUrl = "";
            info=objmes.Envoyer(mess);

            return info;

        }
        private int CreationMessagestatus(long convID, long membrId,int lire)
        {
            messtatu.MessageId = convID;
            messtatu.UserId = membrId;
            messtatu.IsRead = lire;
            messtatu.ReadAt = DateTime.Now;
           
            info =objmes.EnvoyerMessageStatus(messtatu);

            return info;
        }

        private void EnvoieMessagerie()
        {
            if (!string.IsNullOrWhiteSpace(txtMessage.Value))
            {
                long senderId = ide;
                long destinataireId = Convert.ToInt64(lblId.Text);
                bool isGroup = hdnIsGroup.Value == "1";
                int info=0,info1 = 0,info2=0;

                if (isGroup)
                {//il faut continuer l'implementation
                    // Groupe : conversation déjà existante via id du groupe
                    long idGroupe = destinataireId;

                    // Ici, on ajoute le message à la table des messages de groupe
                  ///  info = objmes.AjouterMessageDansGroupe(idGroupe, senderId, txtMessage.Value);
                  //  if (info == 1)
                   //     objmes.ChargerMessagesGroupe(rptMessages, idGroupe, 50);
                }
                else
                {
                    CreationParticipantConversation(destinataireId);

                    // Tchat privé
                    long conversationId = objconver.VerifierConversationId(senderId, destinataireId);

                    //if (conversationId <= 0)
                    //    CreationConversation(0, "");

                   
                   // conversationId = objconver.VerifierConversationId(senderId, destinataireId);

                    info=CreationMessage(conversationId,senderId);
                    info1=CreationMessagestatus(conversationId,senderId,1);
                    info2=CreationMessagestatus(conversationId,destinataireId,0);

                    if (info == 1 && info1==1 && info2==1)
                        ChargerMessages();
                }

                if (info != 1)
                    Response.Write("<script>alert('Erreur lors de l’envoi du message.');</script>");
            }
        }
        protected void btnenvoie_ServerClick(object sender, EventArgs e)
        {
            EnvoieMessagerieAvancee();
        }

        private void EnvoieMessagerieAvancee()
        {
            // Vérifier qu'il y a un message ou un fichier
            bool hasMessage = !string.IsNullOrWhiteSpace(txtMessage.Value);
            bool hasFile = fileUploadAttachment.HasFile;

            if (hasMessage || hasFile)
            {
                long senderId = ide;
                if (lblId.Text == "0" || string.IsNullOrEmpty(lblId.Text))
                {
                    Response.Write("<script>alert('Veuillez sélectionner un destinataire.');</script>");
                    return;
                }

                long destinataireId = Convert.ToInt64(lblId.Text);
                bool isGroup = hdnIsGroup.Value == "1";
                int info = 0;
                string attachmentUrl = null;

                // Gérer l'upload de fichier si présent
                if (hasFile)
                {
                    attachmentUrl = UploadAttachment();
                    if (attachmentUrl == null)
                    {
                        return; // L'erreur est déjà affichée dans UploadAttachment
                    }
                }

                try
                {
                    if (isGroup)
                    {
                        // Implémentation des groupes à finaliser
                        Response.Write("<script>alert('Les conversations de groupe ne sont pas encore implémentées.');</script>");
                        return;
                    }
                    else
                    {
                        CreationParticipantConversation(destinataireId);

                        // Tchat privé
                        long conversationId = objconver.VerifierConversationId(senderId, destinataireId);

                        if (conversationId > 0)
                        {
                            // Utiliser la méthode avancée qui gère automatiquement les statuts
                            info = objmes.AjouterMessageEtStatusPourTous(conversationId, senderId,
                                txtMessage.Value?.Trim() ?? "", attachmentUrl);

                            if (info == 1)
                            {
                                // Sauvegarder le contenu du message avant de le vider
                                string messageContent = txtMessage.Value?.Trim() ?? "";

                                // Vider les champs après envoi réussi
                                txtMessage.Value = "";
                                ChargerMessages();

                                // Notifier le destinataire via SignalR
                                if (!string.IsNullOrEmpty(messageContent) || !string.IsNullOrEmpty(attachmentUrl))
                                {
                                    NotifierNouveauMessage(destinataireId, messageContent, attachmentUrl);
                                }

                                // Cacher la section d'attachement et vider le fichier
                                Response.Write("<script>document.getElementById('attachmentSection').style.display = 'none'; scrollToBottom();</script>");

                                // Message de succès discret
                                Response.Write("<script>console.log('Message envoyé avec succès');</script>");
                            }
                            else
                            {
                                Response.Write("<script>alert('Erreur lors de l\\'envoi du message.');</script>");
                            }
                        }
                        else
                        {
                            Response.Write("<script>alert('Impossible de créer ou trouver la conversation.');</script>");
                        }
                    }
                }
                catch (Exception ex)
                {
                    Response.Write("<script>alert('Erreur: " + ex.Message.Replace("'", "\\'") + "');</script>");
                }
            }
            else
            {
                Response.Write("<script>alert('Veuillez saisir un message ou joindre un fichier.');</script>");
            }
        }

        private string UploadAttachment()
        {
            if (fileUploadAttachment.HasFile)
            {
                try
                {
                    string fileName = Path.GetFileName(fileUploadAttachment.FileName);
                    string extension = Path.GetExtension(fileName).ToLower();

                    // Extensions autorisées
                    string[] validExtensions = { ".pdf", ".doc", ".docx", ".txt", ".jpg", ".jpeg", ".png", ".gif", ".zip", ".rar" };

                    if (!validExtensions.Contains(extension))
                    {
                        Response.Write("<script>alert('Type de fichier non autorisé. Extensions autorisées: " + string.Join(", ", validExtensions) + "');</script>");
                        return null;
                    }

                    // Taille maximale : 5MB
                    if (fileUploadAttachment.PostedFile.ContentLength > 5 * 1024 * 1024)
                    {
                        Response.Write("<script>alert('Le fichier ne peut pas dépasser 5MB.');</script>");
                        return null;
                    }

                    // Générer un nom unique pour éviter les conflits
                    string uniqueFileName = DateTime.Now.ToString("yyyyMMddHHmmss") + "_" + fileName;
                    string uploadPath = Server.MapPath("~/file/messages/");

                    // Créer le dossier s'il n'existe pas
                    if (!Directory.Exists(uploadPath))
                    {
                        Directory.CreateDirectory(uploadPath);
                    }

                    string fullPath = Path.Combine(uploadPath, uniqueFileName);
                    fileUploadAttachment.SaveAs(fullPath);

                    // Retourner l'URL relative sans le tilde
                    return "file/messages/" + uniqueFileName;
                }
                catch (Exception ex)
                {
                    Response.Write("<script>alert('Erreur lors de l\\'upload: " + ex.Message + "');</script>");
                    return null;
                }
            }
            return null;
        }
        void EnvoieMessage()
        {
            if (!string.IsNullOrWhiteSpace(txtMessage.Value))
            {
                long conversationreceveurmembre = objconver.VerifierConversationId(ide, Convert.ToInt64(lblId.Text));
               
                if (conversationreceveurmembre <= 0)
                    CreationConversation(0,"");

                long conversationreceveurmembreencore = objconver.VerifierConversationId(ide, Convert.ToInt64(lblId.Text));

                mess.ConversationId = conversationreceveurmembreencore;
                mess.SenderId = ide;
                mess.Contenu = txtMessage.Value.Trim();
                mess.DateEnvoi = DateTime.Now;
                mess.name = "";
                mess.AttachmentUrl = null;

               info= objmes.Envoyer(mess);

                if (info==1)
                {
                    //CreationParticipantConversation();

                 //   Response.Write("<script LANGUAGE=JavaScript>alert('Message envoyé')</script>");

                }
                else
                {
                    Response.Write("<script LANGUAGE=JavaScript>alert('Erreur')</script>");


                }
            }
        }
        void ChargerMessages()
        {
            //chargement des messages
            if (lblId.Text != "0" && !string.IsNullOrEmpty(lblId.Text))
            {
                long senderId = ide;
                long destinataireId = Convert.ToInt64(lblId.Text);
                long conversationId = objconver.VerifierConversationId(senderId, destinataireId);

                if (conversationId > 0)
                {
                    objmes.ChargerMessages(rptMessages, conversationId, 1000);
                    // Marquer les messages comme lus
                    MarquerMessagesCommeLus(conversationId, senderId);
                }
            }
        }

        private void MarquerMessagesCommeLus(long conversationId, long userId)
        {
            // Marquer tous les messages non lus de cette conversation comme lus
            objmes.MarquerTousMessagesCommeLus(conversationId, userId);
        }

        private void ChargerMessagesGroupe(long conversationId)
        {
            // Charger les messages du groupe
            if (conversationId > 0)
            {
                objmes.ChargerMessages(rptMessages, conversationId, 1000);
                // Marquer les messages comme lus pour cet utilisateur
                MarquerMessagesCommeLus(conversationId, ide);
            }
        }

        protected void btnSearch_Click(object sender, EventArgs e)
        {
            if (!string.IsNullOrWhiteSpace(txtSearchMessages.Text))
            {
                try
                {
                    string searchTerm = txtSearchMessages.Text.Trim();
                    DateTime? searchDate = null;

                    if (!string.IsNullOrWhiteSpace(txtSearchDate.Text))
                    {
                        searchDate = Convert.ToDateTime(txtSearchDate.Text);
                    }

                    // Effectuer la recherche
                    objmes.RechercherMessages(listSearchResults, ide, searchTerm, searchDate);

                    Response.Write("<script>showNotification('Recherche effectuée!', 'success');</script>");
                }
                catch (Exception ex)
                {
                    Response.Write("<script>showNotification('Erreur lors de la recherche: " + ex.Message.Replace("'", "\\'") + "', 'error');</script>");
                }
            }
            else
            {
                Response.Write("<script>showNotification('Veuillez saisir un terme de recherche.', 'error');</script>");
            }
        }

        // Méthodes helper pour l'affichage
        protected long GetCurrentUserId()
        {
            return ide;
        }

        protected string GetFileName(string filePath)
        {
            if (string.IsNullOrEmpty(filePath))
                return "";

            return Path.GetFileName(filePath);
        }

        protected string GetFileSize(string filePath)
        {
            if (string.IsNullOrEmpty(filePath))
                return "";

            try
            {
                // Gérer les chemins avec ou sans ~/
                string physicalPath;
                if (filePath.StartsWith("~/"))
                {
                    physicalPath = Server.MapPath(filePath);
                }
                else if (filePath.StartsWith("file/"))
                {
                    physicalPath = Server.MapPath("~/" + filePath);
                }
                else
                {
                    physicalPath = Server.MapPath("~/file/messages/" + filePath);
                }

                if (File.Exists(physicalPath))
                {
                    FileInfo fileInfo = new FileInfo(physicalPath);
                    long bytes = fileInfo.Length;

                    if (bytes < 1024)
                        return bytes + " B";
                    else if (bytes < 1024 * 1024)
                        return (bytes / 1024).ToString("F1") + " KB";
                    else
                        return (bytes / (1024 * 1024)).ToString("F1") + " MB";
                }
            }
            catch
            {
                // Ignore errors
            }

            return "";
        }

        protected string ResolveAttachmentUrl(string attachmentUrl)
        {
            if (string.IsNullOrEmpty(attachmentUrl))
                return "";

            // Si l'URL commence par ~/, la résoudre
            if (attachmentUrl.StartsWith("~/"))
            {
                return ResolveUrl(attachmentUrl);
            }
            // Si l'URL commence par file/, ajouter le chemin racine
            else if (attachmentUrl.StartsWith("file/"))
            {
                return ResolveUrl("~/" + attachmentUrl);
            }
            // Sinon, considérer que c'est juste le nom du fichier
            else
            {
                return ResolveUrl("~/file/messages/" + attachmentUrl);
            }
        }

        private void NotifierNouveauMessage(long destinataireId, string message, string attachmentUrl)
        {
            try
            {
                // Obtenir les informations de l'expéditeur
                var expediteur = objmem.GetMembreById(ide);
                string senderName = expediteur?.nom + " " + expediteur?.prenom ?? "Utilisateur";
                string senderPhoto = expediteur?.photo ?? "images/default-avatar.png";

                // Nettoyer le message pour JavaScript
                string cleanMessage = message.Replace("'", "\\'").Replace("\"", "\\\"").Replace("\n", "\\n").Replace("\r", "");
                string cleanSenderName = senderName.Replace("'", "\\'").Replace("\"", "\\\"");
                string cleanSenderPhoto = senderPhoto.Replace("'", "\\'").Replace("\"", "\\\"");

                // Créer le script JavaScript pour notifier via SignalR
                string script = $@"
                    <script>
                        setTimeout(function() {{
                            if (typeof connection !== 'undefined' && connection.state === 1) {{
                                connection.invoke('SendMessage', '{destinataireId}', '{cleanMessage}', '{cleanSenderName}', '{cleanSenderPhoto}')
                                    .then(function() {{
                                        console.log('Message envoyé via SignalR au destinataire {destinataireId}');
                                    }})
                                    .catch(function(err) {{
                                        console.error('Erreur envoi SignalR:', err);
                                    }});
                            }} else {{
                                console.log('SignalR non connecté (état: ' + (typeof connection !== 'undefined' ? connection.state : 'undefined') + ')');
                                // Réessayer dans 1 seconde
                                setTimeout(function() {{
                                    if (typeof connection !== 'undefined' && connection.state === 1) {{
                                        connection.invoke('SendMessage', '{destinataireId}', '{cleanMessage}', '{cleanSenderName}', '{cleanSenderPhoto}');
                                    }}
                                }}, 1000);
                            }}
                        }}, 500);
                    </script>";

                Response.Write(script);
            }
            catch (Exception ex)
            {
                // Log l'erreur mais ne pas interrompre le processus
                Response.Write($"<script>console.error('Erreur SignalR: {ex.Message.Replace("'", "\\'")}');</script>");
            }
        }

        // Méthode pour obtenir le nombre de messages non lus
        protected int GetUnreadMessageCount(long membreId)
        {
            return objmes.CompterNonLus(membreId);
        }

        // Méthode pour formater la date de manière intelligente
        protected string FormatSmartDate(DateTime? date)
        {
            if (!date.HasValue)
                return "";

            var now = DateTime.Now;
            var diff = now - date.Value;

            if (diff.TotalMinutes < 1)
                return "À l'instant";
            else if (diff.TotalMinutes < 60)
                return $"Il y a {(int)diff.TotalMinutes} min";
            else if (diff.TotalHours < 24)
                return $"Il y a {(int)diff.TotalHours}h";
            else if (diff.TotalDays < 7)
                return $"Il y a {(int)diff.TotalDays} jour(s)";
            else
                return date.Value.ToString("dd/MM/yyyy");
        }

    }
}